using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data.Configurations;

public class VehiclePerformanceSummaryConfiguration : IEntityTypeConfiguration<VehiclePerformanceSummary>
{
    public void Configure(EntityTypeBuilder<VehiclePerformanceSummary> builder)
    {
        builder.ToTable("VehiclePerformanceSummaries");

        builder.HasKey(vps => vps.Id);

        builder.Property(vps => vps.Period)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(vps => vps.MostFrequentDriver)
            .HasMaxLength(200);

        builder.Property(vps => vps.TollgateCost)
            .HasPrecision(18, 2);

        builder.Property(vps => vps.MaintenanceCost)
            .HasPrecision(18, 2);

        builder.Property(vps => vps.EstimatedOperationalCost)
            .HasPrecision(18, 2);

        // Indexes
        builder.HasIndex(vps => vps.VehicleId);
        builder.HasIndex(vps => vps.SummaryDate);
        builder.HasIndex(vps => vps.Period);
        builder.HasIndex(vps => new { vps.VehicleId, vps.SummaryDate, vps.Period })
            .IsUnique()
            .HasDatabaseName("IX_VehiclePerformanceSummary_Unique");

        // Relationships
        builder.HasOne(vps => vps.Vehicle)
            .WithMany()
            .HasForeignKey(vps => vps.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

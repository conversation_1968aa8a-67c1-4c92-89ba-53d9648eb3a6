# Vietmap Pulling Job Implementation

## Overview
This document describes the implementation of the Quartz-based job for synchronizing data from the Vietmap Tracking API to the local database.

## Architecture

### Components

```
VietmapPullingJob (Quartz Job)
    ↓
IVietmapSyncService (Service Interface)
    ↓
VietmapSyncService (Service Implementation)
    ↓
IVietmapTrackingApi (Refit Interface)
    ↓
ApplicationDbContext (EF Core)
```

## Job Execution Flow

The `VietmapPullingJob` executes **sequentially** in 4 main steps:

### Step 1: Sync Master Data
Synchronizes relatively static reference data:
- **Vehicles** - Fleet information
- **Drivers** - Driver information
- **Tollgates** - Tollgate locations

**Frequency**: Can be configured to run less frequently (e.g., every 3-6 hours)

### Step 2: Sync Real-time Data
Synchronizes current vehicle positions:
- **Vehicle Statuses** - Latest GPS positions for all vehicles

**Frequency**: Should run frequently (e.g., every 1-5 minutes)

### Step 3: Sync Historical Data (Last 24 Hours)
Synchronizes recent transactional data:
- **Driver Shifts** - Shift records
- **Vehicle Trips** - Trip details
- **Tollgate Reports** - Tollgate passages
- **Telematics Violations** - Driver violations (speeding, harsh braking, etc.)

**Frequency**: Can run every 15-30 minutes

### Step 4: Sync Reports (Yesterday)
Synchronizes aggregated daily data:
- **Daily Reports** - Daily statistics
- **Maintenance Records** - Maintenance schedules

**Frequency**: Can run once or twice daily

## Sequential Execution

The job uses the `[DisallowConcurrentExecution]` attribute to ensure:
- Only one instance runs at a time
- No concurrent database writes
- No API rate limit issues
- Consistent data state

## Configuration

### appsettings.json

```json
{
  "Vietmap": {
    "ApiKey": "YOUR_VIETMAP_API_KEY",
    "PullingCron": "0 */5 * * * ?"
  },
  "ConnectionStrings": {
    "TrackingDBConnection": "Server=...;Database=TMS_Tracking;..."
  }
}
```

### Cron Schedule Examples

| Schedule | Cron Expression | Use Case |
|----------|----------------|----------|
| Every 1 minute | `0 */1 * * * ?` | Development/Testing |
| Every 5 minutes | `0 */5 * * * ?` | Real-time tracking |
| Every 10 minutes | `0 */10 * * * ?` | Standard tracking |
| Every 30 minutes | `0 */30 * * * ?` | Light tracking |
| Every hour | `0 0 * * * ?` | Master data only |
| Once daily at midnight | `0 0 0 * * ?` | Reports only |

## Service Methods

### IVietmapSyncService

```csharp
public interface IVietmapSyncService
{
    // Master Data
    Task<int> SyncVehiclesAsync(CancellationToken cancellationToken = default);
    Task<int> SyncDriversAsync(CancellationToken cancellationToken = default);
    Task<int> SyncTollgatesAsync(CancellationToken cancellationToken = default);

    // Real-time Data
    Task<int> SyncVehicleStatusesAsync(CancellationToken cancellationToken = default);

    // Historical Data
    Task<int> SyncDailyReportsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<int> SyncDriverShiftsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<int> SyncTollgateReportsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<int> SyncVehicleTripsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<int> SyncTelematicsViolationsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    // Maintenance
    Task<int> SyncMaintenanceRecordsAsync(CancellationToken cancellationToken = default);
}
```

## Data Synchronization Strategy

### Upsert Logic

Each sync method follows this pattern:

1. **Fetch data from Vietmap API**
2. **Check if record exists** (by VietmapId or unique key)
3. **Insert or Update**:
   - If not exists → Insert new record
   - If exists → Update existing record
4. **Save changes to database**

### Example: Vehicle Sync

```csharp
var existingVehicle = await _dbContext.Vehicles
    .FirstOrDefaultAsync(v => v.VietmapId == vehicleDto.Id);

if (existingVehicle == null)
{
    // Insert new vehicle
    var vehicle = new Vehicle { ... };
    _dbContext.Vehicles.Add(vehicle);
}
else
{
    // Update existing vehicle
    existingVehicle.Plate = vehicleDto.Plate;
    existingVehicle.GroupName = vehicleDto.GroupName;
    // ... update other fields
}
```

### Duplicate Prevention

- **Vehicles**: Unique by `VietmapId`
- **Drivers**: Unique by `VietmapId`
- **Tollgates**: Unique by `VietmapId`
- **Vehicle Statuses**: Checked by `VehicleId + GpsTime`
- **Daily Reports**: Unique by `VehicleId + Date`
- **Driver Shifts**: Checked by `VehicleId + FromTime`
- **Vehicle Trips**: Checked by `VehicleId + FromTime`
- **Tollgate Reports**: Checked by `VehicleId + FromTime`
- **Telematics Violations**: Checked by `VehicleId + DriverId + ViolationTime`
- **Maintenance Records**: Checked by `VehicleId + MaintenanceTypeId + LastPerformed`

## Error Handling

### Service Level

Each sync method includes try-catch blocks:

```csharp
try
{
    _logger.LogInformation("Starting vehicle synchronization");
    // ... sync logic
    _logger.LogInformation("Successfully synchronized {Count} vehicles", syncCount);
    return syncCount;
}
catch (Exception ex)
{
    _logger.LogError(ex, "Error synchronizing vehicles from Vietmap API");
    throw;
}
```

### Job Level

The job handles errors and logs execution results:

```csharp
try
{
    await SyncMasterDataAsync(cancellationToken);
    await SyncRealTimeDataAsync(cancellationToken);
    // ...
    _logger.LogInformation("Job completed successfully in {Duration:mm\\:ss}", duration);
}
catch (OperationCanceledException)
{
    _logger.LogWarning("Job was cancelled");
    throw;
}
catch (Exception ex)
{
    _logger.LogError(ex, "Job failed after {Duration:mm\\:ss}", duration);
    throw new JobExecutionException(ex, refireImmediately: false);
}
```

### Partial Failure Handling

If one vehicle fails during status sync, the job continues with the next vehicle:

```csharp
foreach (var vehicle in vehicles)
{
    try
    {
        var response = await _vietmapApi.GetVehicleStatusAsync(vehicle.Plate, _apiKey);
        // ... process response
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error syncing status for vehicle {Plate}", vehicle.Plate);
        // Continue with next vehicle
    }
}
```

## Rate Limiting

To avoid overwhelming the Vietmap API:

1. **Small delays between requests**:
   ```csharp
   await Task.Delay(100, cancellationToken); // 100ms delay
   ```

2. **Configurable timeout**:
   ```csharp
   .ConfigureHttpClient(c =>
   {
       c.BaseAddress = new Uri("https://client-api.quanlyxe.vn");
       c.Timeout = TimeSpan.FromMinutes(5);
   });
   ```

3. **DisallowConcurrentExecution**: Prevents multiple job instances

## Monitoring and Logging

### Log Levels

```json
{
  "Logging": {
    "LogLevel": {
      "TMS.TrackingService.Application.Job": "Information",
      "TMS.TrackingService.Application.Services": "Information",
      "Quartz": "Warning"
    }
  }
}
```

### Sample Log Output

```
========================================
Starting Vietmap Pulling Job - Instance: abc123
========================================
--- Step 1: Syncing Master Data ---
1.1 Syncing vehicles...
✓ Synced 50 vehicles
1.2 Syncing drivers...
✓ Synced 120 drivers
1.3 Syncing tollgates...
✓ Synced 45 tollgates
--- Master Data Sync Completed ---
--- Step 2: Syncing Real-time Data ---
2.1 Syncing vehicle statuses...
✓ Synced 50 vehicle statuses
--- Real-time Data Sync Completed ---
--- Step 3: Syncing Historical Data (Last 24 Hours) ---
3.1 Syncing driver shifts...
✓ Synced 75 driver shifts
3.2 Syncing vehicle trips...
✓ Synced 230 vehicle trips
3.3 Syncing tollgate reports...
✓ Synced 45 tollgate reports
3.4 Syncing telematics violations...
✓ Synced 12 telematics violations
--- Historical Data Sync Completed ---
--- Step 4: Syncing Reports (Yesterday) ---
4.1 Syncing daily reports...
✓ Synced 50 daily reports
4.2 Syncing maintenance records...
✓ Synced 8 maintenance records
--- Reports Sync Completed ---
========================================
Vietmap Pulling Job completed successfully in 02:35
========================================
```

## Quartz Configuration

### Program.cs Setup

```csharp
// Configure Quartz with clustered setup (SQL Server based)
var connString = builder.Configuration.GetConnectionString("TrackingDBConnection");
builder.Services.AddClusteredQuartz(connString, schedulerName: $"{serviceName}_Scheduler");

// Add Vietmap pulling job
var cronSchedule = builder.Configuration["Vietmap:PullingCron"] ?? "0 */5 * * * ?";
builder.Services.AddQuartz(q =>
{
    var jobKey = new JobKey("VietmapPullingJob");

    q.AddJob<VietmapPullingJob>(opts => opts.WithIdentity(jobKey));

    q.AddTrigger(opts => opts
        .ForJob(jobKey)
        .WithIdentity("VietmapPullingJob-trigger")
        .WithCronSchedule(cronSchedule));
});
```

### Clustered Quartz Benefits

- **High Availability**: Multiple instances can be deployed
- **Load Distribution**: Jobs distributed across instances
- **Failover**: Automatic failover if one instance fails
- **Persistence**: Job state persisted in SQL Server

## Performance Considerations

### Optimization Strategies

1. **Batch Processing**
   - Process vehicles in batches
   - Use `SaveChangesAsync()` after processing batches

2. **Selective Syncing**
   - Master data: Sync less frequently
   - Real-time data: Sync more frequently
   - Historical data: Sync recent data only

3. **Indexes**
   - All lookup fields have indexes
   - Composite indexes for common queries

4. **Connection Pooling**
   - EF Core handles connection pooling automatically
   - Refit client reuses HttpClient

### Estimated Execution Times

Based on fleet size:

| Fleet Size | Master Data | Real-time | Historical | Reports | Total |
|------------|-------------|-----------|------------|---------|-------|
| 10 vehicles | 5s | 10s | 20s | 10s | 45s |
| 50 vehicles | 15s | 30s | 1m | 30s | 2m 15s |
| 100 vehicles | 30s | 1m | 2m | 1m | 4m 30s |
| 500 vehicles | 2m | 5m | 10m | 3m | 20m |

## Troubleshooting

### Common Issues

#### 1. API Key Invalid
```
Error: HTTP 401 Unauthorized
Solution: Check Vietmap:ApiKey in appsettings.json
```

#### 2. Rate Limiting
```
Error: HTTP 429 Too Many Requests
Solution: Increase delay between requests or reduce job frequency
```

#### 3. Timeout Issues
```
Error: Task was cancelled (timeout)
Solution: Increase HttpClient.Timeout or reduce batch size
```

#### 4. Database Deadlocks
```
Error: Deadlock detected
Solution: Ensure DisallowConcurrentExecution is set
```

#### 5. Missing Vehicles
```
Warning: No vehicles found in local database for status sync
Solution: Run master data sync first (Step 1)
```

## Testing

### Manual Job Execution

You can trigger the job manually via Quartz admin UI or programmatically:

```csharp
var schedulerFactory = app.Services.GetRequiredService<ISchedulerFactory>();
var scheduler = await schedulerFactory.GetScheduler();
var jobKey = new JobKey("VietmapPullingJob");
await scheduler.TriggerJob(jobKey);
```

### Integration Testing

```csharp
[Fact]
public async Task VietmapPullingJob_Should_Sync_All_Data()
{
    // Arrange
    var job = new VietmapPullingJob(_syncService, _logger);
    var context = CreateJobExecutionContext();

    // Act
    await job.Execute(context);

    // Assert
    var vehicles = await _dbContext.Vehicles.CountAsync();
    Assert.True(vehicles > 0);
}
```

## Future Enhancements

1. **Delta Sync**: Only sync changed records
2. **Webhooks**: Real-time push from Vietmap
3. **Retry Policy**: Polly-based retry for transient failures
4. **Circuit Breaker**: Protect against cascading failures
5. **Metrics**: Prometheus metrics for monitoring
6. **Health Checks**: Expose job health status
7. **Manual Triggers**: API endpoints to trigger specific syncs
8. **Backfill Jobs**: Separate jobs for historical data backfill

## Summary

The Vietmap Pulling Job provides:
- ✅ **Sequential execution** for data consistency
- ✅ **Comprehensive error handling** for reliability
- ✅ **Flexible scheduling** via Quartz
- ✅ **Detailed logging** for monitoring
- ✅ **Rate limiting** for API protection
- ✅ **Duplicate prevention** for data integrity
- ✅ **Partial failure handling** for resilience
- ✅ **Clustered support** for high availability

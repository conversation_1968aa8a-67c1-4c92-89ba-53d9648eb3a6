﻿using FluentValidation;

namespace TMS.TrackingService.Application.Features.Tracking.Queries;

public class GetVehicleStatusValidator : AbstractValidator<GetVehicleStatusQuery>
{
    public GetVehicleStatusValidator()
    {
        RuleFor(x => x.Plate)
            .NotEmpty()
            .WithMessage("Plate is required")
            .Must(id => id != String.Empty)
            .WithMessage("Plate must be a valid '50H00000'");
    }
}

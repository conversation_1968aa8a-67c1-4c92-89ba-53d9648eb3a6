-- Vietmap Tracking Service Database Schema
-- This script provides the complete database schema for all tracking entities

-- =============================================
-- 1. Vehicles Table
-- =============================================
CREATE TABLE [dbo].[Vehicles] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VietmapId] INT NOT NULL,
    [Plate] NVARCHAR(20) NOT NULL,
    [ActualPlate] NVARCHAR(20) NOT NULL,
    [GroupId] INT NOT NULL,
    [GroupName] NVARCHAR(200) NOT NULL,
    [VehicleTypeId] INT NOT NULL,
    [VehicleTypeName] NVARCHAR(100) NOT NULL,
    [Capacity] FLOAT NOT NULL,
    [Vin] NVARCHAR(50) NULL,
    [BrandName] NVARCHAR(100) NULL,
    [ProductYear] NVARCHAR(10) NULL,
    [RegisterDate] DATETIME2 NULL,
    [MaxSpeed] FLOAT NULL,
    [FuelPer100km] FLOAT NOT NULL DEFAULT 0,
    [FuelPerIdleHour] FLOAT NOT NULL DEFAULT 0,
    [FuelPerIgnitionHour] FLOAT NOT NULL DEFAULT 0,
    [EmissionsHour] INT NOT NULL DEFAULT 0,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [UK_Vehicles_VietmapId] UNIQUE ([VietmapId])
);

CREATE INDEX [IX_Vehicles_Plate] ON [dbo].[Vehicles] ([Plate]);
CREATE INDEX [IX_Vehicles_GroupId] ON [dbo].[Vehicles] ([GroupId]);

-- =============================================
-- 2. VehicleStatuses Table
-- =============================================
CREATE TABLE [dbo].[VehicleStatuses] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VehicleId] UNIQUEIDENTIFIER NOT NULL,
    [GpsTime] DATETIME2 NOT NULL,
    [SysTime] DATETIME2 NOT NULL,
    [X] FLOAT NOT NULL, -- Longitude
    [Y] FLOAT NOT NULL, -- Latitude
    [Status] INT NOT NULL DEFAULT 0, -- Bitflags: 0x1=Engine, 0x2=Door, 0x4=AC, 0x8=Passenger, 0x10=SOS
    [Speed] INT NOT NULL DEFAULT 0,
    [Heading] INT NOT NULL DEFAULT 0,
    [EventId] INT NULL,
    [Distance] BIGINT NOT NULL DEFAULT 0,
    [DriverName] NVARCHAR(200) NULL,
    [LicenseNo] NVARCHAR(50) NULL,
    [Address] NVARCHAR(500) NULL,
    [SensorsJson] NVARCHAR(MAX) NULL,
    [ContainerId] NVARCHAR(50) NULL,
    [ContainerName] NVARCHAR(200) NULL,
    [ContainerSerial] NVARCHAR(100) NULL,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [FK_VehicleStatuses_Vehicles] FOREIGN KEY ([VehicleId])
        REFERENCES [dbo].[Vehicles]([Id]) ON DELETE CASCADE
);

CREATE INDEX [IX_VehicleStatuses_VehicleId] ON [dbo].[VehicleStatuses] ([VehicleId]);
CREATE INDEX [IX_VehicleStatuses_GpsTime] ON [dbo].[VehicleStatuses] ([GpsTime]);
CREATE INDEX [IX_VehicleStatuses_VehicleId_GpsTime] ON [dbo].[VehicleStatuses] ([VehicleId], [GpsTime]);

-- =============================================
-- 3. DailyReports Table
-- =============================================
CREATE TABLE [dbo].[DailyReports] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VehicleId] UNIQUEIDENTIFIER NOT NULL,
    [Date] DATETIME2 NOT NULL,
    [Distance] BIGINT NOT NULL DEFAULT 0,
    [DoorOpenCount] INT NOT NULL DEFAULT 0,
    [OverSpeedCount] INT NOT NULL DEFAULT 0,
    [MaxSpeed] INT NOT NULL DEFAULT 0,
    [FirstAccOnTime] DATETIME2 NULL,
    [LastAccOffTime] DATETIME2 NULL,
    [AccTime] INT NOT NULL DEFAULT 0, -- seconds
    [RunTime] INT NOT NULL DEFAULT 0, -- seconds
    [IdleTime] INT NOT NULL DEFAULT 0, -- seconds
    [StopTime] INT NOT NULL DEFAULT 0, -- seconds
    [SysTime] DATETIME2 NOT NULL,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [FK_DailyReports_Vehicles] FOREIGN KEY ([VehicleId])
        REFERENCES [dbo].[Vehicles]([Id]) ON DELETE CASCADE
);

CREATE INDEX [IX_DailyReports_VehicleId] ON [dbo].[DailyReports] ([VehicleId]);
CREATE INDEX [IX_DailyReports_Date] ON [dbo].[DailyReports] ([Date]);
CREATE UNIQUE INDEX [UK_DailyReports_VehicleId_Date] ON [dbo].[DailyReports] ([VehicleId], [Date]);

-- =============================================
-- 4. Drivers Table
-- =============================================
CREATE TABLE [dbo].[Drivers] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VietmapId] INT NOT NULL,
    [Code] NVARCHAR(50) NOT NULL,
    [Rfid] NVARCHAR(50) NULL,
    [Name] NVARCHAR(200) NOT NULL,
    [Phone] NVARCHAR(20) NULL,
    [Email] NVARCHAR(100) NULL,
    [Address] NVARCHAR(500) NULL,
    [LicenseNo] NVARCHAR(50) NULL,
    [LicenseExpireDate] DATETIME2 NULL,
    [StartDate] DATETIME2 NULL,
    [EndDate] DATETIME2 NULL,
    [Remark] NVARCHAR(1000) NULL,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [UK_Drivers_VietmapId] UNIQUE ([VietmapId])
);

CREATE INDEX [IX_Drivers_Code] ON [dbo].[Drivers] ([Code]);
CREATE INDEX [IX_Drivers_LicenseNo] ON [dbo].[Drivers] ([LicenseNo]);

-- =============================================
-- 5. DriverShifts Table
-- =============================================
CREATE TABLE [dbo].[DriverShifts] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VehicleId] UNIQUEIDENTIFIER NOT NULL,
    [DriverId] UNIQUEIDENTIFIER NOT NULL,
    [Plate] NVARCHAR(20) NOT NULL,
    [DriverName] NVARCHAR(200) NOT NULL,
    [LicenseNo] NVARCHAR(50) NULL,
    [IdNo] NVARCHAR(50) NULL,
    [FromTime] DATETIME2 NOT NULL,
    [ToTime] DATETIME2 NOT NULL,
    [FromAddress] NVARCHAR(500) NULL,
    [ToAddress] NVARCHAR(500) NULL,
    [Distance] BIGINT NOT NULL DEFAULT 0,
    [DoorOpenCount] INT NOT NULL DEFAULT 0,
    [DoorCloseCount] INT NOT NULL DEFAULT 0,
    [OverSpeedCount] INT NOT NULL DEFAULT 0,
    [MaxSpeed] INT NOT NULL DEFAULT 0,
    [AccTime] INT NOT NULL DEFAULT 0,
    [IdleTime] INT NOT NULL DEFAULT 0,
    [RunTime] INT NOT NULL DEFAULT 0,
    [StopTime] INT NOT NULL DEFAULT 0,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [FK_DriverShifts_Vehicles] FOREIGN KEY ([VehicleId])
        REFERENCES [dbo].[Vehicles]([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_DriverShifts_Drivers] FOREIGN KEY ([DriverId])
        REFERENCES [dbo].[Drivers]([Id]) ON DELETE NO ACTION
);

CREATE INDEX [IX_DriverShifts_VehicleId] ON [dbo].[DriverShifts] ([VehicleId]);
CREATE INDEX [IX_DriverShifts_DriverId] ON [dbo].[DriverShifts] ([DriverId]);
CREATE INDEX [IX_DriverShifts_FromTime] ON [dbo].[DriverShifts] ([FromTime]);
CREATE INDEX [IX_DriverShifts_VehicleId_DriverId_FromTime] ON [dbo].[DriverShifts] ([VehicleId], [DriverId], [FromTime]);

-- =============================================
-- 6. VehicleTrips Table
-- =============================================
CREATE TABLE [dbo].[VehicleTrips] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VehicleId] UNIQUEIDENTIFIER NOT NULL,
    [DriverId] UNIQUEIDENTIFIER NULL,
    [FromTime] DATETIME2 NOT NULL,
    [ToTime] DATETIME2 NOT NULL,
    [Duration] INT NOT NULL DEFAULT 0,
    [FromAddress] NVARCHAR(500) NULL,
    [ToAddress] NVARCHAR(500) NULL,
    [DriverName] NVARCHAR(200) NULL,
    [Waypoint] INT NOT NULL DEFAULT 0,
    [Status] INT NOT NULL DEFAULT 0,
    [FromX] FLOAT NOT NULL,
    [FromY] FLOAT NOT NULL,
    [FromRegionId] INT NOT NULL DEFAULT 0,
    [ToX] FLOAT NOT NULL,
    [ToY] FLOAT NOT NULL,
    [ToRegionId] INT NOT NULL DEFAULT 0,
    [GpsDistance] BIGINT NOT NULL DEFAULT 0,
    [MaxSpeed] INT NOT NULL DEFAULT 0,
    [MinSpeed] INT NOT NULL DEFAULT 0,
    [AverageSpeed] INT NOT NULL DEFAULT 0,
    [OverSpeed] INT NOT NULL DEFAULT 0,
    [GpsMileage] BIGINT NOT NULL DEFAULT 0,
    [FromMileage] BIGINT NOT NULL DEFAULT 0,
    [ToMileage] BIGINT NOT NULL DEFAULT 0,
    [DoorOpenCount] INT NOT NULL DEFAULT 0,
    [DoorCloseCount] INT NOT NULL DEFAULT 0,
    [StartAtStation] BIT NOT NULL DEFAULT 0,
    [EndAtStation] BIT NOT NULL DEFAULT 0,
    [StopTime] BIGINT NOT NULL DEFAULT 0,
    [FromDistance] BIGINT NOT NULL DEFAULT 0,
    [ToDistance] BIGINT NOT NULL DEFAULT 0,
    [RestTime] INT NOT NULL DEFAULT 0,
    [LicenseNo] NVARCHAR(50) NULL,
    [RunTime] INT NOT NULL DEFAULT 0,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [FK_VehicleTrips_Vehicles] FOREIGN KEY ([VehicleId])
        REFERENCES [dbo].[Vehicles]([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_VehicleTrips_Drivers] FOREIGN KEY ([DriverId])
        REFERENCES [dbo].[Drivers]([Id]) ON DELETE SET NULL
);

CREATE INDEX [IX_VehicleTrips_VehicleId] ON [dbo].[VehicleTrips] ([VehicleId]);
CREATE INDEX [IX_VehicleTrips_FromTime] ON [dbo].[VehicleTrips] ([FromTime]);
CREATE INDEX [IX_VehicleTrips_VehicleId_FromTime] ON [dbo].[VehicleTrips] ([VehicleId], [FromTime]);

-- =============================================
-- 7. Tollgates Table
-- =============================================
CREATE TABLE [dbo].[Tollgates] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VietmapId] NVARCHAR(50) NOT NULL,
    [Name] NVARCHAR(200) NOT NULL,
    [Address] NVARCHAR(500) NULL,
    [X] FLOAT NOT NULL, -- Longitude
    [Y] FLOAT NOT NULL, -- Latitude
    [Heading] INT NOT NULL DEFAULT 0,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [UK_Tollgates_VietmapId] UNIQUE ([VietmapId])
);

CREATE INDEX [IX_Tollgates_Name] ON [dbo].[Tollgates] ([Name]);

-- =============================================
-- 8. TollgateReports Table
-- =============================================
CREATE TABLE [dbo].[TollgateReports] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VehicleId] UNIQUEIDENTIFIER NOT NULL,
    [DriverId] UNIQUEIDENTIFIER NULL,
    [FromTollgateId] UNIQUEIDENTIFIER NULL,
    [ToTollgateId] UNIQUEIDENTIFIER NULL,
    [FromTime] DATETIME2 NOT NULL,
    [ToTime] DATETIME2 NOT NULL,
    [Duration] INT NOT NULL DEFAULT 0,
    [FromTollgateName] NVARCHAR(200) NOT NULL,
    [ToTollgateName] NVARCHAR(200) NOT NULL,
    [TollgatePrice] DECIMAL(18, 2) NOT NULL DEFAULT 0,
    [FromAddress] NVARCHAR(500) NULL,
    [ToAddress] NVARCHAR(500) NULL,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [FK_TollgateReports_Vehicles] FOREIGN KEY ([VehicleId])
        REFERENCES [dbo].[Vehicles]([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_TollgateReports_Drivers] FOREIGN KEY ([DriverId])
        REFERENCES [dbo].[Drivers]([Id]) ON DELETE SET NULL,
    CONSTRAINT [FK_TollgateReports_FromTollgate] FOREIGN KEY ([FromTollgateId])
        REFERENCES [dbo].[Tollgates]([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_TollgateReports_ToTollgate] FOREIGN KEY ([ToTollgateId])
        REFERENCES [dbo].[Tollgates]([Id]) ON DELETE NO ACTION
);

CREATE INDEX [IX_TollgateReports_VehicleId] ON [dbo].[TollgateReports] ([VehicleId]);
CREATE INDEX [IX_TollgateReports_FromTime] ON [dbo].[TollgateReports] ([FromTime]);
CREATE INDEX [IX_TollgateReports_VehicleId_FromTime] ON [dbo].[TollgateReports] ([VehicleId], [FromTime]);

-- =============================================
-- 9. TelematicsViolations Table
-- =============================================
CREATE TABLE [dbo].[TelematicsViolations] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VehicleId] UNIQUEIDENTIFIER NOT NULL,
    [DriverId] UNIQUEIDENTIFIER NOT NULL,
    [ViolationTime] DATETIME2 NOT NULL,
    [BehaviorType] NVARCHAR(50) NOT NULL, -- HARSH_ACCELERATION, HARSH_BREAK, OVERSPEED, OVERDRIVING4H, OVERDRIVING10H
    [Address] NVARCHAR(500) NULL,
    [Longitude] FLOAT NULL,
    [Latitude] FLOAT NULL,
    [BehaviorDescriptionJson] NVARCHAR(MAX) NULL,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [FK_TelematicsViolations_Vehicles] FOREIGN KEY ([VehicleId])
        REFERENCES [dbo].[Vehicles]([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_TelematicsViolations_Drivers] FOREIGN KEY ([DriverId])
        REFERENCES [dbo].[Drivers]([Id]) ON DELETE NO ACTION
);

CREATE INDEX [IX_TelematicsViolations_VehicleId] ON [dbo].[TelematicsViolations] ([VehicleId]);
CREATE INDEX [IX_TelematicsViolations_DriverId] ON [dbo].[TelematicsViolations] ([DriverId]);
CREATE INDEX [IX_TelematicsViolations_ViolationTime] ON [dbo].[TelematicsViolations] ([ViolationTime]);
CREATE INDEX [IX_TelematicsViolations_BehaviorType] ON [dbo].[TelematicsViolations] ([BehaviorType]);

-- =============================================
-- 10. MaintenanceRecords Table
-- =============================================
CREATE TABLE [dbo].[MaintenanceRecords] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [VehicleId] UNIQUEIDENTIFIER NOT NULL,
    [MaintenanceTypeId] INT NOT NULL,
    [MaintenanceType] NVARCHAR(200) NOT NULL,
    [LastPerformed] DATETIME2 NULL,
    [NextDueDate] DATETIME2 NULL,
    [DueDistance] BIGINT NOT NULL DEFAULT 0,
    [ActualDistance] BIGINT NOT NULL DEFAULT 0,
    [ActualDistanceDate] DATETIME2 NULL,
    [LastMileage] BIGINT NOT NULL DEFAULT 0,
    [Completed] BIT NOT NULL DEFAULT 0,
    [CompletedDate] DATETIME2 NULL,
    [DateWarning] DATETIME2 NULL,
    [DistanceWarning] BIGINT NOT NULL DEFAULT 0,
    [Amount] DECIMAL(18, 2) NOT NULL DEFAULT 0,
    [Remark] NVARCHAR(1000) NULL,
    [Inactive] BIT NOT NULL DEFAULT 0,
    [Created] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Updated] DATETIME2 NULL,
    [CreatedBy] NVARCHAR(100) NULL,
    [UpdatedBy] NVARCHAR(100) NULL,
    CONSTRAINT [FK_MaintenanceRecords_Vehicles] FOREIGN KEY ([VehicleId])
        REFERENCES [dbo].[Vehicles]([Id]) ON DELETE CASCADE
);

CREATE INDEX [IX_MaintenanceRecords_VehicleId] ON [dbo].[MaintenanceRecords] ([VehicleId]);
CREATE INDEX [IX_MaintenanceRecords_MaintenanceTypeId] ON [dbo].[MaintenanceRecords] ([MaintenanceTypeId]);
CREATE INDEX [IX_MaintenanceRecords_NextDueDate] ON [dbo].[MaintenanceRecords] ([NextDueDate]);
CREATE INDEX [IX_MaintenanceRecords_VehicleId_Completed] ON [dbo].[MaintenanceRecords] ([VehicleId], [Completed]);

-- =============================================
-- Sample Queries
-- =============================================

-- Get latest vehicle status
/*
SELECT TOP 1 *
FROM VehicleStatuses
WHERE VehicleId = @VehicleId
ORDER BY GpsTime DESC;
*/

-- Get daily summary for a vehicle
/*
SELECT *
FROM DailyReports
WHERE VehicleId = @VehicleId
  AND Date >= @FromDate
  AND Date <= @ToDate
ORDER BY Date DESC;
*/

-- Get driver violations
/*
SELECT v.*, d.Name as DriverName, veh.Plate
FROM TelematicsViolations v
INNER JOIN Drivers d ON v.DriverId = d.Id
INNER JOIN Vehicles veh ON v.VehicleId = veh.Id
WHERE v.DriverId = @DriverId
  AND v.ViolationTime >= @FromDate
  AND v.ViolationTime <= @ToDate
ORDER BY v.ViolationTime DESC;
*/

-- Get pending maintenance
/*
SELECT m.*, v.Plate
FROM MaintenanceRecords m
INNER JOIN Vehicles v ON m.VehicleId = v.Id
WHERE m.Completed = 0
  AND m.Inactive = 0
  AND (m.NextDueDate <= DATEADD(DAY, 7, GETUTCDATE()) OR m.DistanceWarning <= 1000)
ORDER BY m.NextDueDate;
*/

using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Driver shift report entity
/// </summary>
public class DriverShift : AuditableEntity<Guid>
{
    public Guid VehicleId { get; set; }
    public Guid DriverId { get; set; }
    public string Plate { get; set; } = string.Empty;
    public string DriverName { get; set; } = string.Empty;
    public string? LicenseNo { get; set; }
    public string? IdNo { get; set; }
    public DateTime FromTime { get; set; }
    public DateTime ToTime { get; set; }
    public string? FromAddress { get; set; }
    public string? ToAddress { get; set; }
    public long Distance { get; set; }
    public int DoorOpenCount { get; set; }
    public int DoorCloseCount { get; set; }
    public int OverSpeedCount { get; set; }
    public int MaxSpeed { get; set; }
    public int AccTime { get; set; }
    public int IdleTime { get; set; }
    public int RunTime { get; set; }
    public int StopTime { get; set; }

    // Navigation properties
    public Vehicle Vehicle { get; set; } = null!;
    public Driver Driver { get; set; } = null!;
}

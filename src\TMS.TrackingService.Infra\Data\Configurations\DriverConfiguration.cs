using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data.Configurations;

public class DriverConfiguration : IEntityTypeConfiguration<Driver>
{
    public void Configure(EntityTypeBuilder<Driver> builder)
    {
        builder.ToTable("Drivers");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.VietmapId)
            .IsRequired();

        builder.Property(d => d.Code)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(d => d.Rfid)
            .HasMaxLength(50);

        builder.Property(d => d.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(d => d.Phone)
            .HasMaxLength(20);

        builder.Property(d => d.Email)
            .HasMaxLength(100);

        builder.Property(d => d.Address)
            .HasMaxLength(500);

        builder.Property(d => d.LicenseNo)
            .HasMaxLength(50);

        builder.Property(d => d.Remark)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(d => d.VietmapId)
            .IsUnique();

        builder.HasIndex(d => d.Code);

        builder.HasIndex(d => d.LicenseNo);

        // Relationships
        builder.HasMany(d => d.DriverShifts)
            .WithOne(ds => ds.Driver)
            .HasForeignKey(ds => ds.DriverId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(d => d.TelematicsViolations)
            .WithOne(tv => tv.Driver)
            .HasForeignKey(tv => tv.DriverId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

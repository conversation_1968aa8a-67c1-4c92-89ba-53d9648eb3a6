# TMS Tracking Service

A comprehensive .NET 9.0 Web API service for tracking vehicles and managing todos, built with Clean Architecture principles.

## Description

TMS Tracking Service is a microservice that provides vehicle tracking capabilities and todo management functionality. It integrates with external APIs for real-time vehicle status and offers a full CRUD API for todo items. The service is designed with scalability, observability, and maintainability in mind.

## Features

- **Vehicle Tracking**: Retrieve real-time vehicle status using license plate numbers
- **Todo Management**: Complete CRUD operations for todo items
- **Clean Architecture**: Organized into Api, Application, Domain, and Infrastructure layers
- **API Documentation**: Swagger/OpenAPI integration for easy API exploration
- **Observability**: OpenTelemetry integration for distributed tracing
- **Logging**: Structured logging with Serilog
- **Authentication**: JWT-based authentication with policy-based authorization
- **Validation**: FluentValidation for request validation
- **Database**: PostgreSQL with Entity Framework Core
- **External API Integration**: Refit client for third-party API calls

## Architecture

The project follows Clean Architecture principles with the following layers:

- **Api**: Controllers, middleware, and API-specific configurations
- **Application**: Business logic, commands, queries, and handlers (CQRS with MediatR)
- **Domain**: Entities, value objects, and domain logic
- **Infrastructure**: Data access, repositories, and external service integrations
- **Contracts**: Request/response DTOs and API contracts
- **Shared**: Common utilities and shared kernel packages

## Prerequisites

- .NET 9.0 SDK
- PostgreSQL database
- Git (for cloning the repository)

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd TMS.TrackingService
   ```

2. Restore NuGet packages:
   ```bash
   dotnet restore
   ```

3. Build the solution:
   ```bash
   dotnet build
   ```

## Configuration

Update the `appsettings.json` file in the `src/TMS.TrackingService.Api` directory with your configuration:

```json
{
  "ConnectionStrings": {
    "TrackingDBConnection": "Host=your-host;Database=your-db;Username=your-user;Password=your-password"
  },
  "Identity": {
    "Issuer": "your-issuer",
    "Audience": "your-audience",
    "Key": "your-jwt-secret-key"
  },
  "OpenTelemetry": {
    "OtlpEndpoint": "your-otlp-endpoint",
    "Key": "your-otlp-key"
  },
  "Vietmap": {
    "apiKey": "your-vietmap-api-key"
  }
}
```

## Running the Application

1. Ensure PostgreSQL is running and the database is accessible.

2. Run the application:
   ```bash
   dotnet run --project src/TMS.TrackingService.Api
   ```

3. The API will be available at `https://localhost:5001` (or the port configured in `launchSettings.json`).

4. In development mode, Swagger UI is available at `https://localhost:5001/swagger`.

## API Documentation

### Todos Endpoints

- `GET /api/v1/todos` - Retrieve all todos
- `GET /api/v1/todos/{id}` - Retrieve a specific todo by ID
- `POST /api/v1/todos` - Create a new todo
- `PUT /api/v1/todos/{id}` - Update an existing todo
- `PATCH /api/v1/todos/{id}/complete` - Mark a todo as completed
- `DELETE /api/v1/todos/{id}` - Delete a todo

### Tracking Endpoints

- `GET /api/v1/trackings/{plate}` - Get vehicle status by license plate

### Request/Response Examples

#### Create Todo
```http
POST /api/v1/todos
Content-Type: application/json

{
  "title": "Sample Todo",
  "description": "This is a sample todo item"
}
```

#### Get Vehicle Status
```http
GET /api/v1/trackings/ABC-123
```

## Database

The application uses PostgreSQL and Entity Framework Core. The database schema is automatically created on startup using `EnsureCreated()`. For production environments, consider using migrations:

```bash
dotnet ef migrations add InitialCreate --project src/TMS.TrackingService.Infra --startup-project src/TMS.TrackingService.Api
dotnet ef database update --project src/TMS.TrackingService.Infra --startup-project src/TMS.TrackingService.Api
```

## Testing

Run the tests using:
```bash
dotnet test
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
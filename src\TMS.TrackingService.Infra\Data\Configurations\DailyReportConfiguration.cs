using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data.Configurations;

public class DailyReportConfiguration : IEntityTypeConfiguration<DailyReport>
{
    public void Configure(EntityTypeBuilder<DailyReport> builder)
    {
        builder.ToTable("DailyReports");

        builder.<PERSON><PERSON><PERSON>(dr => dr.Id);

        builder.Property(dr => dr.VehicleId)
            .IsRequired();

        builder.Property(dr => dr.Date)
            .IsRequired();

        // Indexes
        builder.HasIndex(dr => dr.VehicleId);

        builder.HasIndex(dr => dr.Date);

        builder.HasIndex(dr => new { dr.VehicleId, dr.Date })
            .IsUnique();

        // Relationship
        builder.HasOne(dr => dr.Vehicle)
            .WithMany(v => v.DailyReports)
            .HasForeignKey(dr => dr.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

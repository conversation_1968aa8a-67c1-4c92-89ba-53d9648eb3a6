﻿namespace TMS.TruckService.Application;

public class PermissionDefinition
{
    public const string TodoRead   = "Todo.Read";
    public const string TodoWrite  = "Todo.Write";
    public const string TodoDelete = "Todo.Delete";
    public const string TodoCreate = "Todo.Create";
    public const string TodoUpdate = "Todo.Update";

   public static IEnumerable<string> AllPermissions => new List<string>
    {
        TodoRead,
        TodoWrite,
        TodoDelete,
        TodoCreate,
        TodoUpdate
    };
}

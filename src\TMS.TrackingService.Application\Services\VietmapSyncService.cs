using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using TMS.TrackingService.ApiClient;
using TMS.TrackingService.Domain.Entities;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Services;

public class VietmapSyncService : IVietmapSyncService
{
    private readonly IVietmapTrackingApi _vietmapApi;
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<VietmapSyncService> _logger;

    public VietmapSyncService(
        IVietmapTrackingApi vietmapApi,
        ApplicationDbContext dbContext,
        ILogger<VietmapSyncService> logger)
    {
        _vietmapApi = vietmapApi;
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<int> SyncVehiclesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting vehicle synchronization from Vietmap API");

            var response = await _vietmapApi.GetVehiclesAsync();
            if (response?.Data == null || !response.Data.Any())
            {
                _logger.LogWarning("No vehicles returned from Vietmap API");
                return 0;
            }

            var syncCount = 0;
            foreach (var vehicleDto in response.Data)
            {
                var existingVehicle = await _dbContext.Vehicles
                    .FirstOrDefaultAsync(v => v.VietmapId == vehicleDto.Id, cancellationToken);

                if (existingVehicle == null)
                {
                    // Create new vehicle
                    var vehicle = new Vehicle
                    {
                        Id = Guid.NewGuid(),
                        VietmapId = vehicleDto.Id,
                        Plate = vehicleDto.Plate,
                        ActualPlate = vehicleDto.ActualPlate,
                        GroupId = vehicleDto.GroupId,
                        GroupName = vehicleDto.GroupName,
                        VehicleTypeId = vehicleDto.VehicleTypeId,
                        VehicleTypeName = vehicleDto.VehicleTypeName,
                        Capacity = vehicleDto.Capacity,
                        Vin = vehicleDto.Vin,
                        BrandName = vehicleDto.BrandName,
                        ProductYear = vehicleDto.ProductYear,
                        RegisterDate = vehicleDto.RegisterDate,
                        MaxSpeed = vehicleDto.MaxSpeed,
                        FuelPer100km = vehicleDto.FuelPer100km,
                        FuelPerIdleHour = vehicleDto.FuelPerIdleHour,
                        FuelPerIgnitionHour = vehicleDto.FuelPerIgnitionHour,
                        EmissionsHour = vehicleDto.EmissionsHour
                    };

                    _dbContext.Vehicles.Add(vehicle);
                    syncCount++;
                }
                else
                {
                    // Update existing vehicle
                    existingVehicle.Plate = vehicleDto.Plate;
                    existingVehicle.ActualPlate = vehicleDto.ActualPlate;
                    existingVehicle.GroupId = vehicleDto.GroupId;
                    existingVehicle.GroupName = vehicleDto.GroupName;
                    existingVehicle.VehicleTypeId = vehicleDto.VehicleTypeId;
                    existingVehicle.VehicleTypeName = vehicleDto.VehicleTypeName;
                    existingVehicle.Capacity = vehicleDto.Capacity;
                    existingVehicle.Vin = vehicleDto.Vin;
                    existingVehicle.BrandName = vehicleDto.BrandName;
                    existingVehicle.ProductYear = vehicleDto.ProductYear;
                    existingVehicle.RegisterDate = vehicleDto.RegisterDate;
                    existingVehicle.MaxSpeed = vehicleDto.MaxSpeed;
                    existingVehicle.FuelPer100km = vehicleDto.FuelPer100km;
                    existingVehicle.FuelPerIdleHour = vehicleDto.FuelPerIdleHour;
                    existingVehicle.FuelPerIgnitionHour = vehicleDto.FuelPerIgnitionHour;
                    existingVehicle.EmissionsHour = vehicleDto.EmissionsHour;

                    syncCount++;
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} vehicles", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing vehicles from Vietmap API");
            throw;
        }
    }

    public async Task<int> SyncDriversAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting driver synchronization from Vietmap API");

            var response = await _vietmapApi.GetDriversAsync();
            if (response?.Data == null || !response.Data.Any())
            {
                _logger.LogWarning("No drivers returned from Vietmap API");
                return 0;
            }

            var syncCount = 0;
            foreach (var driverDto in response.Data)
            {
                var existingDriver = await _dbContext.Drivers
                    .FirstOrDefaultAsync(d => d.VietmapId == driverDto.Id, cancellationToken);

                if (existingDriver == null)
                {
                    // Create new driver
                    var driver = new Driver
                    {
                        Id = Guid.NewGuid(),
                        VietmapId = driverDto.Id,
                        Code = driverDto.Code,
                        Rfid = driverDto.Rfid,
                        Name = driverDto.Name,
                        Phone = driverDto.Phone,
                        Email = driverDto.Email,
                        Address = driverDto.Address,
                        LicenseNo = driverDto.LicenseNo,
                        LicenseExpireDate = driverDto.LicenseExpireDate,
                        StartDate = driverDto.StartDate,
                        EndDate = driverDto.EndDate,
                        Remark = driverDto.Remark
                    };

                    _dbContext.Drivers.Add(driver);
                    syncCount++;
                }
                else
                {
                    // Update existing driver
                    existingDriver.Code = driverDto.Code;
                    existingDriver.Rfid = driverDto.Rfid;
                    existingDriver.Name = driverDto.Name;
                    existingDriver.Phone = driverDto.Phone;
                    existingDriver.Email = driverDto.Email;
                    existingDriver.Address = driverDto.Address;
                    existingDriver.LicenseNo = driverDto.LicenseNo;
                    existingDriver.LicenseExpireDate = driverDto.LicenseExpireDate;
                    existingDriver.StartDate = driverDto.StartDate;
                    existingDriver.EndDate = driverDto.EndDate;
                    existingDriver.Remark = driverDto.Remark;

                    syncCount++;
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} drivers", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing drivers from Vietmap API");
            throw;
        }
    }

    public async Task<int> SyncTollgatesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting tollgate synchronization from Vietmap API");

            var response = await _vietmapApi.GetTollgatesAsync();
            if (response?.Data == null || !response.Data.Any())
            {
                _logger.LogWarning("No tollgates returned from Vietmap API");
                return 0;
            }

            var syncCount = 0;
            foreach (var tollgateDto in response.Data)
            {
                var existingTollgate = await _dbContext.Tollgates
                    .FirstOrDefaultAsync(t => t.VietmapId == tollgateDto.Id, cancellationToken);

                if (existingTollgate == null)
                {
                    // Create new tollgate
                    var tollgate = new Tollgate
                    {
                        Id = Guid.NewGuid(),
                        VietmapId = tollgateDto.Id,
                        Name = tollgateDto.Name,
                        Address = tollgateDto.Address,
                        X = tollgateDto.X,
                        Y = tollgateDto.Y,
                        Heading = tollgateDto.Heading
                    };

                    _dbContext.Tollgates.Add(tollgate);
                    syncCount++;
                }
                else
                {
                    // Update existing tollgate
                    existingTollgate.Name = tollgateDto.Name;
                    existingTollgate.Address = tollgateDto.Address;
                    existingTollgate.X = tollgateDto.X;
                    existingTollgate.Y = tollgateDto.Y;
                    existingTollgate.Heading = tollgateDto.Heading;

                    syncCount++;
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} tollgates", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing tollgates from Vietmap API");
            throw;
        }
    }

    public async Task<int> SyncVehicleStatusesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting vehicle status synchronization from Vietmap API");

            // Get all vehicles from local database
            var vehicles = await _dbContext.Vehicles.ToListAsync(cancellationToken);
            if (!vehicles.Any())
            {
                _logger.LogWarning("No vehicles found in local database for status sync");
                return 0;
            }

            var syncCount = 0;
            foreach (var vehicle in vehicles)
            {
                try
                {
                    var response = await _vietmapApi.GetVehicleStatusAsync(vehicle.Plate);
                    if (response?.Data == null || !response.Data.Any())
                    {
                        _logger.LogDebug("No status data for vehicle {Plate}", vehicle.Plate);
                        continue;
                    }

                    foreach (var statusDto in response.Data)
                    {
                        // Check if this exact status already exists
                        var exists = await _dbContext.VehicleStatuses
                            .AnyAsync(vs => vs.VehicleId == vehicle.Id && vs.GpsTime == statusDto.GpsTime, cancellationToken);

                        if (!exists)
                        {
                            var vehicleStatus = new VehicleStatus
                            {
                                Id = Guid.NewGuid(),
                                VehicleId = vehicle.Id,
                                GpsTime = statusDto.GpsTime,
                                SysTime = statusDto.SysTime,
                                X = statusDto.X,
                                Y = statusDto.Y,
                                Status = statusDto.Status,
                                Speed = statusDto.Speed,
                                Heading = statusDto.Heading,
                                EventId = statusDto.EventId,
                                Distance = statusDto.Distance,
                                DriverName = statusDto.Driver,
                                LicenseNo = statusDto.LicenseNo,
                                Address = statusDto.Address,
                                SensorsJson = statusDto.Sensors != null ? JsonSerializer.Serialize(statusDto.Sensors) : null,
                                ContainerId = statusDto.ContainerId,
                                ContainerName = statusDto.ContainerName,
                                ContainerSerial = statusDto.ContainerSerial
                            };

                            _dbContext.VehicleStatuses.Add(vehicleStatus);
                            syncCount++;
                        }
                    }

                    // Small delay to avoid rate limiting
                    await Task.Delay(100, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error syncing status for vehicle {Plate}", vehicle.Plate);
                    // Continue with next vehicle
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} vehicle statuses", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing vehicle statuses from Vietmap API");
            throw;
        }
    }

    public async Task<int> SyncDailyReportsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting daily reports synchronization from {FromDate} to {ToDate}", fromDate, toDate);

            var vehicles = await _dbContext.Vehicles.ToListAsync(cancellationToken);
            if (!vehicles.Any())
            {
                _logger.LogWarning("No vehicles found for daily reports sync");
                return 0;
            }

            var syncCount = 0;
            var from = fromDate.ToString("yyyyMMddHHmmss");
            var to = toDate.ToString("yyyyMMddHHmmss");

            foreach (var vehicle in vehicles)
            {
                try
                {
                    var response = await _vietmapApi.GetDailyReportsAsync(vehicle.VietmapId, from, to);
                    if (response?.Data == null || !response.Data.Any())
                    {
                        continue;
                    }

                    foreach (var reportDto in response.Data)
                    {
                        var existingReport = await _dbContext.DailyReports
                            .FirstOrDefaultAsync(dr => dr.VehicleId == vehicle.Id && dr.Date.Date == reportDto.Date.Date, cancellationToken);

                        if (existingReport == null)
                        {
                            var dailyReport = new DailyReport
                            {
                                Id = Guid.NewGuid(),
                                VehicleId = vehicle.Id,
                                Date = reportDto.Date,
                                Distance = reportDto.Distance,
                                DoorOpenCount = reportDto.DoorOpenCount,
                                OverSpeedCount = reportDto.OverSpeedCount,
                                MaxSpeed = reportDto.MaxSpeed,
                                FirstAccOnTime = reportDto.FirstAccOnTime,
                                LastAccOffTime = reportDto.LastAccOffTime,
                                AccTime = reportDto.AccTime,
                                RunTime = reportDto.RunTime,
                                IdleTime = reportDto.IdleTime,
                                StopTime = reportDto.StopTime,
                                SysTime = reportDto.SysTime
                            };

                            _dbContext.DailyReports.Add(dailyReport);
                            syncCount++;
                        }
                        else
                        {
                            // Update existing report
                            existingReport.Distance = reportDto.Distance;
                            existingReport.DoorOpenCount = reportDto.DoorOpenCount;
                            existingReport.OverSpeedCount = reportDto.OverSpeedCount;
                            existingReport.MaxSpeed = reportDto.MaxSpeed;
                            existingReport.FirstAccOnTime = reportDto.FirstAccOnTime;
                            existingReport.LastAccOffTime = reportDto.LastAccOffTime;
                            existingReport.AccTime = reportDto.AccTime;
                            existingReport.RunTime = reportDto.RunTime;
                            existingReport.IdleTime = reportDto.IdleTime;
                            existingReport.StopTime = reportDto.StopTime;
                            existingReport.SysTime = reportDto.SysTime;
                        }
                    }

                    await Task.Delay(100, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error syncing daily reports for vehicle {VietmapId}", vehicle.VietmapId);
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} daily reports", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing daily reports from Vietmap API");
            throw;
        }
    }

    public async Task<int> SyncDriverShiftsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting driver shifts synchronization from {FromDate} to {ToDate}", fromDate, toDate);

            var vehicles = await _dbContext.Vehicles.ToListAsync(cancellationToken);
            if (!vehicles.Any())
            {
                _logger.LogWarning("No vehicles found for driver shifts sync");
                return 0;
            }

            var syncCount = 0;
            var from = fromDate.ToString("yyyyMMddHHmmss");
            var to = toDate.ToString("yyyyMMddHHmmss");

            foreach (var vehicle in vehicles)
            {
                try
                {
                    var response = await _vietmapApi.GetVehicleShiftReportsAsync(vehicle.VietmapId, from, to);
                    if (response?.Data == null || !response.Data.Any())
                    {
                        continue;
                    }

                    foreach (var shiftDto in response.Data)
                    {
                        // Find or create driver
                        Driver? driver = null;
                        if (shiftDto.DriverId.HasValue)
                        {
                            driver = await _dbContext.Drivers
                                .FirstOrDefaultAsync(d => d.VietmapId == shiftDto.DriverId.Value, cancellationToken);
                        }

                        // Check if shift already exists
                        var exists = await _dbContext.DriverShifts
                            .AnyAsync(ds => ds.VehicleId == vehicle.Id && ds.FromTime == shiftDto.FromTime, cancellationToken);

                        if (!exists)
                        {
                            var driverShift = new DriverShift
                            {
                                Id = Guid.NewGuid(),
                                VehicleId = vehicle.Id,
                                DriverId = driver?.Id ?? Guid.Empty,
                                Plate = shiftDto.Plate,
                                DriverName = shiftDto.Driver ?? "Unknown",
                                LicenseNo = shiftDto.LicenseNo,
                                IdNo = shiftDto.IdNo,
                                FromTime = shiftDto.FromTime,
                                ToTime = shiftDto.ToTime,
                                FromAddress = shiftDto.From,
                                ToAddress = shiftDto.To,
                                Distance = shiftDto.Distance,
                                DoorOpenCount = shiftDto.DoorOpenCount,
                                DoorCloseCount = shiftDto.DoorCloseCount,
                                OverSpeedCount = shiftDto.OverSpeedCount,
                                MaxSpeed = shiftDto.MaxSpeed,
                                AccTime = shiftDto.AccTime,
                                IdleTime = shiftDto.IdleTime,
                                RunTime = shiftDto.RunTime,
                                StopTime = shiftDto.StopTime
                            };

                            _dbContext.DriverShifts.Add(driverShift);
                            syncCount++;
                        }
                    }

                    await Task.Delay(100, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error syncing driver shifts for vehicle {VietmapId}", vehicle.VietmapId);
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} driver shifts", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing driver shifts from Vietmap API");
            throw;
        }
    }

    public async Task<int> SyncTollgateReportsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting tollgate reports synchronization from {FromDate} to {ToDate}", fromDate, toDate);

            var vehicles = await _dbContext.Vehicles.ToListAsync(cancellationToken);
            if (!vehicles.Any())
            {
                _logger.LogWarning("No vehicles found for tollgate reports sync");
                return 0;
            }

            var syncCount = 0;
            var from = fromDate.ToString("yyyyMMddHHmmss");
            var to = toDate.ToString("yyyyMMddHHmmss");

            foreach (var vehicle in vehicles)
            {
                try
                {
                    var response = await _vietmapApi.GetVehicleTollgateReportsAsync(vehicle.VietmapId, from, to);
                    if (response?.Data == null || !response.Data.Any())
                    {
                        continue;
                    }

                    foreach (var reportDto in response.Data)
                    {
                        // Check if report already exists
                        var exists = await _dbContext.TollgateReports
                            .AnyAsync(tr => tr.VehicleId == vehicle.Id && tr.FromTime == reportDto.FromTime, cancellationToken);

                        if (!exists)
                        {
                            // Find tollgates
                            var fromTollgate = await _dbContext.Tollgates
                                .FirstOrDefaultAsync(t => t.VietmapId == reportDto.FromTollgateId, cancellationToken);
                            var toTollgate = await _dbContext.Tollgates
                                .FirstOrDefaultAsync(t => t.VietmapId == reportDto.ToTollgateId, cancellationToken);

                            var tollgateReport = new TollgateReport
                            {
                                Id = Guid.NewGuid(),
                                VehicleId = vehicle.Id,
                                DriverId = null, // Can be linked if needed
                                FromTollgateId = fromTollgate?.Id,
                                ToTollgateId = toTollgate?.Id,
                                FromTime = reportDto.FromTime,
                                ToTime = reportDto.ToTime,
                                Duration = reportDto.Duration,
                                FromTollgateName = reportDto.FromTollgate,
                                ToTollgateName = reportDto.ToTollgate,
                                TollgatePrice = reportDto.TollgatePrice,
                                FromAddress = reportDto.FromAddress,
                                ToAddress = reportDto.ToAddress
                            };

                            _dbContext.TollgateReports.Add(tollgateReport);
                            syncCount++;
                        }
                    }

                    await Task.Delay(100, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error syncing tollgate reports for vehicle {VietmapId}", vehicle.VietmapId);
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} tollgate reports", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing tollgate reports from Vietmap API");
            throw;
        }
    }

    public async Task<int> SyncVehicleTripsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting vehicle trips synchronization from {FromDate} to {ToDate}", fromDate, toDate);

            var vehicles = await _dbContext.Vehicles.ToListAsync(cancellationToken);
            if (!vehicles.Any())
            {
                _logger.LogWarning("No vehicles found for trips sync");
                return 0;
            }

            var syncCount = 0;
            var from = fromDate.ToString("yyyyMMddHHmmss");
            var to = toDate.ToString("yyyyMMddHHmmss");
            var vehicleIds = string.Join(",", vehicles.Select(v => v.VietmapId));

            var response = await _vietmapApi.GetVehicleTripReportAsync(vehicleIds, from, to);
            if (response?.Data == null || !response.Data.Any())
            {
                _logger.LogWarning("No trips returned from Vietmap API");
                return 0;
            }

            foreach (var tripDto in response.Data)
            {
                var vehicle = vehicles.FirstOrDefault(v => v.VietmapId == tripDto.VehicleId);
                if (vehicle == null) continue;

                // Check if trip already exists
                var exists = await _dbContext.VehicleTrips
                    .AnyAsync(vt => vt.VehicleId == vehicle.Id &&
                                   vt.FromTime == DateTimeOffset.FromUnixTimeSeconds(tripDto.FromTime).DateTime,
                            cancellationToken);

                if (!exists)
                {
                    var vehicleTrip = new VehicleTrip
                    {
                        Id = Guid.NewGuid(),
                        VehicleId = vehicle.Id,
                        DriverId = null,
                        FromTime = DateTimeOffset.FromUnixTimeSeconds(tripDto.FromTime).DateTime,
                        ToTime = DateTimeOffset.FromUnixTimeSeconds(tripDto.ToTime).DateTime,
                        Duration = tripDto.Duration,
                        FromAddress = tripDto.From,
                        ToAddress = tripDto.To,
                        DriverName = tripDto.Driver,
                        Waypoint = tripDto.Waypoint,
                        Status = tripDto.Status,
                        FromX = tripDto.FromX,
                        FromY = tripDto.FromY,
                        FromRegionId = tripDto.FromRegionId,
                        ToX = tripDto.ToX,
                        ToY = tripDto.ToY,
                        ToRegionId = tripDto.ToRegionId,
                        GpsDistance = tripDto.GpsDistance,
                        MaxSpeed = tripDto.MaxSpeed,
                        MinSpeed = tripDto.MinSpeed,
                        AverageSpeed = tripDto.AverageSpeed,
                        OverSpeed = tripDto.OverSpeed,
                        GpsMileage = tripDto.GpsMileage,
                        FromMileage = tripDto.FromMileage,
                        ToMileage = tripDto.ToMileage,
                        DoorOpenCount = tripDto.DoorOpenCount,
                        DoorCloseCount = tripDto.DoorCloseCount,
                        StartAtStation = tripDto.StartAtStation,
                        EndAtStation = tripDto.EndAtStation,
                        StopTime = tripDto.StopTime,
                        FromDistance = tripDto.FromDistance,
                        ToDistance = tripDto.ToDistance,
                        RestTime = tripDto.RestTime,
                        LicenseNo = tripDto.LicenseNo,
                        RunTime = tripDto.RunTime
                    };

                    _dbContext.VehicleTrips.Add(vehicleTrip);
                    syncCount++;
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} vehicle trips", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing vehicle trips from Vietmap API");
            throw;
        }
    }

    public async Task<int> SyncTelematicsViolationsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting telematics violations synchronization from {FromDate} to {ToDate}", fromDate, toDate);

            var from = fromDate.ToString("yyyyMMdd");
            var to = toDate.ToString("yyyyMMdd");

            var response = await _vietmapApi.GetTelematicsViolationAsync(null, from, to);
            if (response?.Data == null || !response.Data.Any())
            {
                _logger.LogWarning("No violations returned from Vietmap API");
                return 0;
            }

            var syncCount = 0;
            foreach (var violationDto in response.Data)
            {
                // Find vehicle
                var vehicle = await _dbContext.Vehicles
                    .FirstOrDefaultAsync(v => v.VietmapId == violationDto.VehicleId, cancellationToken);
                if (vehicle == null) continue;

                // Find driver
                var driver = await _dbContext.Drivers
                    .FirstOrDefaultAsync(d => d.VietmapId == violationDto.DriverId, cancellationToken);
                if (driver == null) continue;

                // Check if violation already exists
                var exists = await _dbContext.TelematicsViolations
                    .AnyAsync(tv => tv.VehicleId == vehicle.Id &&
                                   tv.DriverId == driver.Id &&
                                   tv.ViolationTime == violationDto.DateTime,
                            cancellationToken);

                if (!exists)
                {
                    var violation = new TelematicsViolation
                    {
                        Id = Guid.NewGuid(),
                        VehicleId = vehicle.Id,
                        DriverId = driver.Id,
                        ViolationTime = violationDto.DateTime,
                        BehaviorType = violationDto.BehaviorType,
                        Address = violationDto.Address,
                        BehaviorDescriptionJson = violationDto.BehaviorDescription != null
                            ? JsonSerializer.Serialize(violationDto.BehaviorDescription)
                            : null
                    };

                    _dbContext.TelematicsViolations.Add(violation);
                    syncCount++;
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} telematics violations", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing telematics violations from Vietmap API");
            throw;
        }
    }

    public async Task<int> SyncMaintenanceRecordsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting maintenance records synchronization from Vietmap API");

            var response = await _vietmapApi.GetMaintenanceManagementAsync();
            if (response?.Data == null || !response.Data.Any())
            {
                _logger.LogWarning("No maintenance records returned from Vietmap API");
                return 0;
            }

            var syncCount = 0;
            foreach (var maintenanceDto in response.Data)
            {
                // Find vehicle
                var vehicle = await _dbContext.Vehicles
                    .FirstOrDefaultAsync(v => v.VietmapId == maintenanceDto.VehicleId, cancellationToken);
                if (vehicle == null) continue;

                // Check if record already exists
                var existingRecord = await _dbContext.MaintenanceRecords
                    .FirstOrDefaultAsync(mr => mr.VehicleId == vehicle.Id &&
                                              mr.MaintenanceTypeId == maintenanceDto.MaintenTypeId &&
                                              mr.LastPerformed == maintenanceDto.LastPerformed,
                                        cancellationToken);

                if (existingRecord == null)
                {
                    var maintenanceRecord = new MaintenanceRecord
                    {
                        Id = Guid.NewGuid(),
                        VehicleId = vehicle.Id,
                        MaintenanceTypeId = maintenanceDto.MaintenTypeId,
                        MaintenanceType = maintenanceDto.MaintenType,
                        LastPerformed = maintenanceDto.LastPerformed,
                        NextDueDate = maintenanceDto.NextDueDate,
                        DueDistance = maintenanceDto.DueDistance,
                        ActualDistance = maintenanceDto.ActualDistance,
                        ActualDistanceDate = maintenanceDto.ActualDistanceDate,
                        LastMileage = maintenanceDto.LastMileage,
                        Completed = maintenanceDto.Completed,
                        CompletedDate = maintenanceDto.CompletedDate,
                        DateWarning = maintenanceDto.DateWarning,
                        DistanceWarning = maintenanceDto.DistanceWarning,
                        Amount = maintenanceDto.Amount,
                        Remark = maintenanceDto.Remark,
                        Inactive = maintenanceDto.Inactive
                    };

                    _dbContext.MaintenanceRecords.Add(maintenanceRecord);
                    syncCount++;
                }
                else
                {
                    // Update existing record
                    existingRecord.NextDueDate = maintenanceDto.NextDueDate;
                    existingRecord.DueDistance = maintenanceDto.DueDistance;
                    existingRecord.ActualDistance = maintenanceDto.ActualDistance;
                    existingRecord.ActualDistanceDate = maintenanceDto.ActualDistanceDate;
                    existingRecord.LastMileage = maintenanceDto.LastMileage;
                    existingRecord.Completed = maintenanceDto.Completed;
                    existingRecord.CompletedDate = maintenanceDto.CompletedDate;
                    existingRecord.DateWarning = maintenanceDto.DateWarning;
                    existingRecord.DistanceWarning = maintenanceDto.DistanceWarning;
                    existingRecord.Amount = maintenanceDto.Amount;
                    existingRecord.Remark = maintenanceDto.Remark;
                    existingRecord.Inactive = maintenanceDto.Inactive;
                }
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Successfully synchronized {Count} maintenance records", syncCount);

            return syncCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing maintenance records from Vietmap API");
            throw;
        }
    }
}

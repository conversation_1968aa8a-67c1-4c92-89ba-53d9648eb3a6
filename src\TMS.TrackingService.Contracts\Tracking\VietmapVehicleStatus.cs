﻿namespace TMS.TrackingService.Contracts.Tracking;

public class VietmapVehicleStatus
{
    public long Id { get; set; }
    public DateTimeOffset GpsTime { get; set; }
    public DateTimeOffset SysTime { get; set; }
    public double X { get; set; } // Longitude
    public double Y { get; set; } // Latitude
    public int Status { get; set; }
    public double Speed { get; set; }
    public double Heading { get; set; }
    public string Address { get; set; }
    public List<SensorDto> Sensors { get; set; }
    public long Distance { get; set; }
    public string Url { get; set; }
    public int Satellite { get; set; }
    public string Driver { get; set; }
    public string Plate { get; set; }
    public string ActualPlate { get; set; }
    public int EventId { get; set; }
    public string LicenseNo { get; set; }
    public string ContainerName { get; set; }
    public string ContainerSerial { get; set; }
    public string ContainerId { get; set; }
    public bool IsAccOn { get; set; }
    public bool IsDoorOpen { get; set; }
    public bool IsAirConditionOn { get; set; }
    public bool IsPassenger { get; set; }
    public bool IsSOS { get; set; }
    public bool IsBreakIn { get; set; }
    public bool OverSpeedByRoad { get; set; }
    public bool OverSpeedByVehicleType { get; set; }
    public DateTimeOffset? StopTime { get; set; }
    public string AssetId { get; set; }
    public long LastOdoMile { get; set; }
}

public class SensorDto
{
    public int SensorTypeId { get; set; }
    public double Value { get; set; }
}


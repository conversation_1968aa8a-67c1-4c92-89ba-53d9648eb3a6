namespace TMS.TrackingService.Application.Services;

/// <summary>
/// Service for synchronizing data from Vietmap API to local database
/// </summary>
public interface IVietmapSyncService
{
    /// <summary>
    /// Sync vehicles from Vietmap API
    /// </summary>
    Task<int> SyncVehiclesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync drivers from Vietmap API
    /// </summary>
    Task<int> SyncDriversAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync tollgates from Vietmap API
    /// </summary>
    Task<int> SyncTollgatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync vehicle status (real-time GPS data) for all vehicles
    /// </summary>
    Task<int> SyncVehicleStatusesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync daily reports for specific date range
    /// </summary>
    Task<int> SyncDailyReportsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync driver shifts for specific date range
    /// </summary>
    Task<int> SyncDriverShiftsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync tollgate reports for specific date range
    /// </summary>
    Task<int> SyncTollgateReportsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync vehicle trips for specific date range
    /// </summary>
    Task<int> SyncVehicleTripsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync telematics violations for specific date range
    /// </summary>
    Task<int> SyncTelematicsViolationsAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync maintenance records
    /// </summary>
    Task<int> SyncMaintenanceRecordsAsync(CancellationToken cancellationToken = default);
}

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.TrackingService.Domain.Entities;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Services;

public class VietmapAnalysisService : IVietmapAnalysisService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<VietmapAnalysisService> _logger;

    public VietmapAnalysisService(
        ApplicationDbContext dbContext,
        ILogger<VietmapAnalysisService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<int> CalculateVehiclePerformanceSummariesAsync(
        DateTime fromDate,
        DateTime toDate,
        string period = "Daily",
        CancellationToken cancellationToken = default)
    {
        var count = 0;
        var vehicles = await _dbContext.Vehicles.ToListAsync(cancellationToken);

        foreach (var vehicle in vehicles)
        {
            try
            {
                var summaryDate = period switch
                {
                    "Weekly" => fromDate.AddDays(-(int)fromDate.DayOfWeek),
                    "Monthly" => new DateTime(fromDate.Year, fromDate.Month, 1),
                    _ => fromDate.Date
                };

                // Get all relevant data for the vehicle in the period
                var statuses = await _dbContext.VehicleStatuses
                    .Where(vs => vs.VehicleId == vehicle.Id &&
                                 vs.GpsTime >= fromDate &&
                                 vs.GpsTime < toDate)
                    .OrderBy(vs => vs.GpsTime)
                    .ToListAsync(cancellationToken);

                var dailyReports = await _dbContext.DailyReports
                    .Where(dr => dr.VehicleId == vehicle.Id &&
                                 dr.Date >= fromDate.Date &&
                                 dr.Date < toDate.Date)
                    .ToListAsync(cancellationToken);

                var trips = await _dbContext.VehicleTrips
                    .Where(vt => vt.VehicleId == vehicle.Id &&
                                 vt.FromTime >= fromDate &&
                                 vt.FromTime < toDate)
                    .ToListAsync(cancellationToken);

                var violations = await _dbContext.TelematicsViolations
                    .Where(tv => tv.VehicleId == vehicle.Id &&
                                 tv.ViolationTime >= fromDate &&
                                 tv.ViolationTime < toDate)
                    .ToListAsync(cancellationToken);

                var tollgateReports = await _dbContext.TollgateReports
                    .Where(tr => tr.VehicleId == vehicle.Id &&
                                 tr.FromTime >= fromDate &&
                                 tr.FromTime < toDate)
                    .ToListAsync(cancellationToken);

                // Skip if no data
                if (!statuses.Any() && !dailyReports.Any() && !trips.Any())
                {
                    continue;
                }

                // Check if summary already exists
                var existingSummary = await _dbContext.VehiclePerformanceSummaries
                    .FirstOrDefaultAsync(vps => vps.VehicleId == vehicle.Id &&
                                                 vps.SummaryDate == summaryDate &&
                                                 vps.Period == period,
                        cancellationToken);

                var summary = existingSummary ?? new VehiclePerformanceSummary
                {
                    Id = Guid.NewGuid(),
                    VehicleId = vehicle.Id,
                    SummaryDate = summaryDate,
                    Period = period
                };

                // Calculate Distance Metrics
                summary.TotalDistance = dailyReports.Sum(dr => dr.Distance);
                summary.AverageDistancePerDay = dailyReports.Any()
                    ? dailyReports.Average(dr => dr.Distance)
                    : 0;
                summary.MaxDistanceInDay = dailyReports.Any()
                    ? dailyReports.Max(dr => dr.Distance)
                    : 0;

                // Calculate Time Metrics
                summary.TotalRunTime = dailyReports.Sum(dr => dr.RunTime);
                summary.TotalIdleTime = dailyReports.Sum(dr => dr.IdleTime);
                summary.TotalStopTime = dailyReports.Sum(dr => dr.StopTime);

                var totalTime = summary.TotalRunTime + summary.TotalIdleTime + summary.TotalStopTime;
                summary.UtilizationRate = totalTime > 0
                    ? (double)summary.TotalRunTime / totalTime * 100
                    : 0;

                // Calculate Speed Metrics
                summary.MaxSpeed = statuses.Any() ? statuses.Max(s => s.Speed) : 0;
                summary.AverageSpeed = statuses.Any() ? statuses.Average(s => s.Speed) : 0;
                summary.OverSpeedCount = violations.Count(v => v.BehaviorType.Contains("OVERSPEED"));
                summary.OverSpeedDuration = 0; // Not available in entity

                // Calculate Fuel Metrics (not available in DailyReport entity)
                summary.TotalFuelConsumed = null;
                summary.AverageFuelConsumption = null;
                summary.FuelEfficiencyScore = null;

                // Calculate Trip Metrics
                summary.TotalTrips = trips.Count;
                summary.AverageTripDistance = trips.Any()
                    ? trips.Average(t => t.GpsDistance)
                    : 0;
                summary.AverageTripDuration = trips.Any()
                    ? (int)trips.Average(t => t.Duration)
                    : 0;

                // Calculate Driver Metrics
                var uniqueDrivers = trips
                    .Where(t => t.DriverId.HasValue)
                    .Select(t => t.DriverId!.Value)
                    .Distinct()
                    .ToList();

                summary.UniqueDriversCount = uniqueDrivers.Count;

                if (uniqueDrivers.Any())
                {
                    var driverTrips = trips
                        .Where(t => t.DriverId.HasValue)
                        .GroupBy(t => t.DriverId!.Value)
                        .OrderByDescending(g => g.Count())
                        .FirstOrDefault();

                    if (driverTrips != null)
                    {
                        var mostFrequentDriver = await _dbContext.Drivers
                            .FirstOrDefaultAsync(d => d.Id == driverTrips.Key, cancellationToken);
                        summary.MostFrequentDriver = mostFrequentDriver?.Name;
                    }
                }

                // Calculate Violation Metrics
                summary.TotalViolations = violations.Count;
                summary.HarshAccelerationCount = violations.Count(v => v.BehaviorType.Contains("HARSH_ACCELERATION"));
                summary.HarshBrakingCount = violations.Count(v => v.BehaviorType.Contains("HARSH_BREAK"));
                summary.OverdrivingCount = violations.Count(v => v.BehaviorType.Contains("OVERDRIVING"));

                // Calculate Operational Metrics
                summary.DoorOpenCount = statuses.Count(s => (s.Status & 0x2) != 0); // Door Open flag
                summary.DaysActive = dailyReports
                    .Where(dr => dr.Distance > 0)
                    .Select(dr => dr.Date.Date)
                    .Distinct()
                    .Count();
                summary.FirstActivityTime = statuses.Any() ? statuses.Min(s => s.GpsTime) : null;
                summary.LastActivityTime = statuses.Any() ? statuses.Max(s => s.GpsTime) : null;

                // Calculate Cost Metrics
                summary.TollgateCost = tollgateReports.Sum(tr => tr.TollgatePrice);
                summary.MaintenanceCost = null; // To be calculated from maintenance records
                summary.EstimatedOperationalCost = CalculateEstimatedOperationalCost(
                    summary.TotalDistance,
                    summary.TotalFuelConsumed,
                    summary.TollgateCost);

                if (existingSummary == null)
                {
                    await _dbContext.VehiclePerformanceSummaries.AddAsync(summary, cancellationToken);
                }

                count++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating performance summary for vehicle {VehicleId}", vehicle.Id);
            }
        }

        await _dbContext.SaveChangesAsync(cancellationToken);
        _logger.LogInformation("Calculated {Count} vehicle performance summaries for period {Period}", count, period);

        return count;
    }

    public async Task<int> CalculateDriverPerformanceSummariesAsync(
        DateTime fromDate,
        DateTime toDate,
        string period = "Daily",
        CancellationToken cancellationToken = default)
    {
        var count = 0;
        var drivers = await _dbContext.Drivers.ToListAsync(cancellationToken);

        foreach (var driver in drivers)
        {
            try
            {
                var summaryDate = period switch
                {
                    "Weekly" => fromDate.AddDays(-(int)fromDate.DayOfWeek),
                    "Monthly" => new DateTime(fromDate.Year, fromDate.Month, 1),
                    _ => fromDate.Date
                };

                // Get all relevant data for the driver in the period
                var shifts = await _dbContext.DriverShifts
                    .Where(ds => ds.DriverId == driver.Id &&
                                 ds.FromTime >= fromDate &&
                                 ds.FromTime < toDate)
                    .ToListAsync(cancellationToken);

                var trips = await _dbContext.VehicleTrips
                    .Where(vt => vt.DriverId == driver.Id &&
                                 vt.FromTime >= fromDate &&
                                 vt.FromTime < toDate)
                    .ToListAsync(cancellationToken);

                var violations = await _dbContext.TelematicsViolations
                    .Where(tv => tv.DriverId == driver.Id &&
                                 tv.ViolationTime >= fromDate &&
                                 tv.ViolationTime < toDate)
                    .ToListAsync(cancellationToken);

                // Skip if no data
                if (!shifts.Any() && !trips.Any())
                {
                    continue;
                }

                // Check if summary already exists
                var existingSummary = await _dbContext.DriverPerformanceSummaries
                    .FirstOrDefaultAsync(dps => dps.DriverId == driver.Id &&
                                                 dps.SummaryDate == summaryDate &&
                                                 dps.Period == period,
                        cancellationToken);

                var summary = existingSummary ?? new DriverPerformanceSummary
                {
                    Id = Guid.NewGuid(),
                    DriverId = driver.Id,
                    SummaryDate = summaryDate,
                    Period = period
                };

                // Calculate Work Time Metrics
                summary.TotalShifts = shifts.Count;
                summary.TotalWorkHours = shifts.Sum(s => s.RunTime); // Using RunTime as work hours
                summary.AverageShiftDuration = shifts.Any()
                    ? shifts.Average(s => s.RunTime)
                    : 0;
                summary.FirstShiftStart = shifts.Any() ? shifts.Min(s => s.FromTime) : null;
                summary.LastShiftEnd = shifts.Any() ? shifts.Max(s => s.ToTime) : null;

                // Calculate Distance Metrics
                summary.TotalDistance = trips.Sum(t => t.GpsDistance);
                summary.AverageDistancePerShift = summary.TotalShifts > 0
                    ? (double)summary.TotalDistance / summary.TotalShifts
                    : 0;
                summary.MaxDistanceInShift = trips.Any()
                    ? trips.Max(t => t.GpsDistance)
                    : 0;

                // Calculate Driving Behavior
                summary.AverageSpeed = trips.Any() ? trips.Average(t => t.AverageSpeed) : 0;
                summary.MaxSpeed = trips.Any() ? trips.Max(t => t.MaxSpeed) : 0;
                summary.OverSpeedCount = violations.Count(v => v.BehaviorType.Contains("OVERSPEED"));
                summary.HarshAccelerationCount = violations.Count(v => v.BehaviorType.Contains("HARSH_ACCELERATION"));
                summary.HarshBrakingCount = violations.Count(v => v.BehaviorType.Contains("HARSH_BREAK"));

                // Calculate Safety Score
                summary.TotalViolations = violations.Count;
                summary.CriticalViolations = violations.Count(v =>
                    v.BehaviorType.Contains("OVERSPEED") || v.BehaviorType.Contains("HARSH_BREAK") || v.BehaviorType.Contains("OVERDRIVING"));
                summary.SafetyScore = CalculateSafetyScore(
                    summary.TotalDistance,
                    summary.TotalViolations,
                    summary.CriticalViolations);

                // Calculate Compliance
                summary.OverdrivingDays = shifts
                    .Where(s => s.RunTime > 36000) // >10 hours (in seconds)
                    .Select(s => s.FromTime.Date)
                    .Distinct()
                    .Count();

                summary.ContinuousDrivingViolations = violations.Count(v => v.BehaviorType.Contains("OVERDRIVING"));

                var totalShiftDays = shifts
                    .Select(s => s.FromTime.Date)
                    .Distinct()
                    .Count();

                summary.RestComplianceRate = totalShiftDays > 0 && summary.OverdrivingDays == 0;

                // Calculate Efficiency Metrics
                summary.TotalTrips = trips.Count;
                summary.AverageTripDistance = trips.Any() ? trips.Average(t => t.GpsDistance) : 0;

                // Calculate idle time percentage from shifts
                var totalRunTime = shifts.Sum(s => s.RunTime);
                var totalIdleTime = shifts.Sum(s => s.IdleTime);
                summary.IdleTimePercentage = totalRunTime > 0
                    ? (int)((double)totalIdleTime / totalRunTime * 100)
                    : 0;

                summary.FuelEfficiency = 0; // Not available in entity

                // Calculate Vehicle Usage
                var uniqueVehicles = trips
                    .Where(t => t.VehicleId != Guid.Empty)
                    .Select(t => t.VehicleId)
                    .Distinct()
                    .ToList();

                summary.UniqueVehiclesUsed = uniqueVehicles.Count;

                if (uniqueVehicles.Any())
                {
                    var vehicleTrips = trips
                        .GroupBy(t => t.VehicleId)
                        .OrderByDescending(g => g.Count())
                        .FirstOrDefault();

                    if (vehicleTrips != null)
                    {
                        var mostUsedVehicle = await _dbContext.Vehicles
                            .FirstOrDefaultAsync(v => v.Id == vehicleTrips.Key, cancellationToken);
                        summary.MostUsedVehiclePlate = mostUsedVehicle?.Plate;
                    }
                }

                // Calculate Performance Rating
                summary.PerformanceRating = CalculatePerformanceRating(summary.SafetyScore, summary.IdleTimePercentage);

                if (existingSummary == null)
                {
                    await _dbContext.DriverPerformanceSummaries.AddAsync(summary, cancellationToken);
                }

                count++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating performance summary for driver {DriverId}", driver.Id);
            }
        }

        await _dbContext.SaveChangesAsync(cancellationToken);
        _logger.LogInformation("Calculated {Count} driver performance summaries for period {Period}", count, period);

        return count;
    }

    public async Task<int> CalculateAllPerformanceSummariesAsync(
        DateTime summaryDate,
        string period = "Daily",
        CancellationToken cancellationToken = default)
    {
        var (fromDate, toDate) = GetDateRange(summaryDate, period);

        var vehicleCount = await CalculateVehiclePerformanceSummariesAsync(
            fromDate, toDate, period, cancellationToken);

        var driverCount = await CalculateDriverPerformanceSummariesAsync(
            fromDate, toDate, period, cancellationToken);

        return vehicleCount + driverCount;
    }

    #region Helper Methods

    private static (DateTime fromDate, DateTime toDate) GetDateRange(DateTime summaryDate, string period)
    {
        return period switch
        {
            "Weekly" =>
            (
                summaryDate.AddDays(-(int)summaryDate.DayOfWeek),
                summaryDate.AddDays(7 - (int)summaryDate.DayOfWeek)
            ),
            "Monthly" =>
            (
                new DateTime(summaryDate.Year, summaryDate.Month, 1),
                new DateTime(summaryDate.Year, summaryDate.Month, 1).AddMonths(1)
            ),
            _ => // Daily
            (
                summaryDate.Date,
                summaryDate.Date.AddDays(1)
            )
        };
    }

    private static double? CalculateFuelEfficiencyScore(double? avgConsumption)
    {
        if (!avgConsumption.HasValue || avgConsumption.Value == 0)
            return null;

        // Score based on fuel consumption (lower is better)
        // Excellent: < 8 L/100km
        // Good: 8-12 L/100km
        // Average: 12-16 L/100km
        // Poor: > 16 L/100km
        return avgConsumption.Value switch
        {
            < 8 => 100,
            < 12 => 80,
            < 16 => 60,
            _ => 40
        };
    }

    private static decimal? CalculateEstimatedOperationalCost(
        long totalDistance,
        double? totalFuel,
        decimal? tollgateCost)
    {
        if (totalDistance == 0)
            return null;

        // Estimated costs per km
        const decimal fuelCostPerLiter = 25000m; // VND
        const decimal maintenanceCostPerKm = 500m; // VND

        var distanceKm = totalDistance / 1000m;
        var fuelCost = totalFuel.HasValue
            ? (decimal)totalFuel.Value * fuelCostPerLiter
            : 0;

        var maintenanceCost = distanceKm * maintenanceCostPerKm;

        return fuelCost + maintenanceCost + (tollgateCost ?? 0);
    }

    private static double CalculateSafetyScore(long totalDistance, int totalViolations, int criticalViolations)
    {
        if (totalDistance == 0)
            return 100;

        // Base score
        var score = 100.0;

        // Deduct points based on violations per 1000 km
        var distanceKm = totalDistance / 1000.0;
        var violationsPerThousandKm = totalViolations / Math.Max(distanceKm, 1);
        var criticalViolationsPerThousandKm = criticalViolations / Math.Max(distanceKm, 1);

        // Deduct 2 points per violation, 5 points per critical violation
        score -= violationsPerThousandKm * 2;
        score -= criticalViolationsPerThousandKm * 5;

        return Math.Max(0, Math.Min(100, score));
    }

    private static string CalculatePerformanceRating(double safetyScore, int idleTimePercentage)
    {
        // Combine safety score and idle time efficiency
        var efficiencyScore = Math.Max(0, 100 - idleTimePercentage);
        var overallScore = (safetyScore * 0.7) + (efficiencyScore * 0.3);

        return overallScore switch
        {
            >= 90 => "Excellent",
            >= 75 => "Good",
            >= 60 => "Average",
            _ => "Poor"
        };
    }

    #endregion
}

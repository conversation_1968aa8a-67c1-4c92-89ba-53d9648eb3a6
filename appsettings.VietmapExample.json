{"Vietmap": {"ApiKey": "YOUR_VIETMAP_API_KEY_HERE", "PullingCron": "0 */5 * ? * *", "Comments": {"PullingCron": "Quartz cron expression for Vietmap data pulling job", "Format": "seconds minutes hours day-of-month month day-of-week", "Examples": {"Every1Minute": "0 */1 * ? * *", "Every5Minutes": "0 */5 * ? * *", "Every10Minutes": "0 */10 * ? * *", "Every30Minutes": "0 */30 * ? * *", "EveryHour": "0 0 * ? * *", "Every3Hours": "0 0 */3 ? * *", "OnceDailyAtMidnight": "0 0 0 ? * *", "OnceDailyAt2AM": "0 0 2 ? * *", "EveryWeekdayAt9AM": "0 0 9 ? * MON-FRI"}}}, "ConnectionStrings": {"TrackingDBConnection": "Server=localhost;Database=TMS_Tracking;User Id=sa;Password=YourPassword;TrustServerCertificate=True;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "TMS.TrackingService.Application.Job": "Information", "TMS.TrackingService.Application.Services": "Information", "Quartz": "Warning"}}}
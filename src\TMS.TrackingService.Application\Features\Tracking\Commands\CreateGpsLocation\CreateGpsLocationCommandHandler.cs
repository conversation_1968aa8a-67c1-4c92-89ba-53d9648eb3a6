﻿using Mapster;
using MediatR;
using TMS.TrackingService.Contracts.Tracking;
using TMS.TrackingService.Domain.Entities;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Features.Tracking.Commands.CreateGpsLocation;

public class CreateGpsLocationCommandHandler : IRequestHandler<CreateGpsLocationCommand, GpsLocationResponse>
{
    private readonly ApplicationDbContext _context;

    public CreateGpsLocationCommandHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<GpsLocationResponse> Handle(CreateGpsLocationCommand request, CancellationToken cancellationToken)
    {
        var gpsLocation = new GpsLocation
        {
            Id = Guid.NewGuid(),
            DeviceId = request.DeviceId,
            VehiclePlate = request.VehiclePlate,
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            Altitude = request.Altitude,
            Speed = request.Speed,
            Heading = request.Heading,
            Accuracy = request.Accuracy,
            Timestamp = request.Timestamp,
            BatteryLevel = request.BatteryLevel
        };

        _context.Set<GpsLocation>().Add(gpsLocation);
        await _context.SaveChangesAsync(cancellationToken);

        return gpsLocation.Adapt<GpsLocationResponse>();
    }
}

﻿using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.TrackingService.Contracts.Tracking;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Features.Tracking.Queries;

public class GetVehicleStatusHandler : IRequestHandler<GetVehicleStatusQuery, VehicleStatusResponse>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<GetVehicleStatusHandler> _logger;
    private readonly IMapper _mapper;

    public GetVehicleStatusHandler(ApplicationDbContext dbContext, IMapper mapper, ILogger<GetVehicleStatusHandler> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task<VehicleStatusResponse> Handle(GetVehicleStatusQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Get vehicle by plate
            var vehicle = await _dbContext.Vehicles
                .AsNoTracking()
                .FirstOrDefaultAsync(v => v.ActualPlate == request.Plate, cancellationToken);

            if (vehicle == null)
            {
                _logger.LogWarning("Vehicle with plate {Plate} not found", request.Plate);
                return new VehicleStatusResponse();
            }

            // Get the latest status for this vehicle
            var latestStatus = await _dbContext.VehicleStatuses
                .AsNoTracking()
                .Where(vs => vs.VehicleId == vehicle.Id)
                .OrderByDescending(vs => vs.GpsTime)
                .FirstOrDefaultAsync(cancellationToken);

            if (latestStatus == null)
            {
                _logger.LogWarning("No status found for vehicle {Plate}", request.Plate);
                return new VehicleStatusResponse { Plate = request.Plate };
            }

            return new VehicleStatusResponse
            {
                Plate = request.Plate,
                GpsTime = latestStatus.GpsTime,
                SysTime = latestStatus.SysTime,
                Y = latestStatus.Y, // Latitude
                X = latestStatus.X, // Longitude
                Status = latestStatus.Status,
                Speed = latestStatus.Speed,
                Heading = latestStatus.Heading,
                Address = latestStatus.Address ?? string.Empty,
                Url = string.Empty,
                Driver = latestStatus.DriverName ?? string.Empty,
                LicenseNo = latestStatus.LicenseNo ?? string.Empty
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching vehicle status for plate {Plate}", request.Plate);
            return new VehicleStatusResponse();
        }
    }
}

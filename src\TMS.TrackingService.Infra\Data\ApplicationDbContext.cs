﻿using Microsoft.EntityFrameworkCore;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data;

public class ApplicationDbContext : BaseDbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, ICurrentFactorProvider currentFactorProvider)
        : base(options, currentFactorProvider)
    {
    }

    // Vietmap Tracking entities
    public DbSet<Vehicle> Vehicles { get; set; }
    public DbSet<VehicleStatus> VehicleStatuses { get; set; }
    public DbSet<DailyReport> DailyReports { get; set; }
    public DbSet<Driver> Drivers { get; set; }
    public DbSet<DriverShift> DriverShifts { get; set; }
    public DbSet<VehicleTrip> VehicleTrips { get; set; }
    public DbSet<Tollgate> Tollgates { get; set; }
    public DbSet<TollgateReport> TollgateReports { get; set; }
    public DbSet<TelematicsViolation> TelematicsViolations { get; set; }
    public DbSet<MaintenanceRecord> MaintenanceRecords { get; set; }
    public DbSet<GpsLocation> GpsLocations { get; set; }

    // Analysis/Summary entities
    public DbSet<VehiclePerformanceSummary> VehiclePerformanceSummaries { get; set; }
    public DbSet<DriverPerformanceSummary> DriverPerformanceSummaries { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
    }
}

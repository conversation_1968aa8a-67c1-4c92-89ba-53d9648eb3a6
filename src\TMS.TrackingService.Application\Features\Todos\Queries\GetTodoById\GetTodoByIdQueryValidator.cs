﻿using FluentValidation;

namespace TMS.TrackingService.Application.Features.Todos.Queries.GetTodoById;

public class GetVehicleStatusValidator : AbstractValidator<GetTodoByIdQuery>
{
    public GetVehicleStatusValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("Todo ID is required")
            .Must(id => id != Guid.Empty)
            .WithMessage("Todo ID must be a valid GUID");
    }
}

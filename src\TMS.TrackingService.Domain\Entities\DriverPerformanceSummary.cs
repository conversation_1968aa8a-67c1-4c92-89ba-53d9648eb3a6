using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Aggregated driver performance metrics
/// </summary>
public class DriverPerformanceSummary : AuditableEntity<Guid>
{
    public Guid DriverId { get; set; }
    public DateTime SummaryDate { get; set; }
    public string Period { get; set; } = string.Empty; // Daily, Weekly, Monthly

    // Work Time Metrics
    public int TotalShifts { get; set; }
    public int TotalWorkHours { get; set; } // seconds
    public double AverageShiftDuration { get; set; }
    public DateTime? FirstShiftStart { get; set; }
    public DateTime? LastShiftEnd { get; set; }

    // Distance Metrics
    public long TotalDistance { get; set; }
    public double AverageDistancePerShift { get; set; }
    public long MaxDistanceInShift { get; set; }

    // Driving Behavior
    public double AverageSpeed { get; set; }
    public int MaxSpeed { get; set; }
    public int OverSpeedCount { get; set; }
    public int HarshAccelerationCount { get; set; }
    public int HarshBrakingCount { get; set; }

    // Safety Score
    public double SafetyScore { get; set; } // 0-100
    public int TotalViolations { get; set; }
    public int CriticalViolations { get; set; }

    // Compliance
    public int OverdrivingDays { get; set; } // Days with >10h driving
    public int ContinuousDrivingViolations { get; set; } // >4h without break
    public bool RestComplianceRate { get; set; }

    // Efficiency Metrics
    public int TotalTrips { get; set; }
    public double AverageTripDistance { get; set; }
    public int IdleTimePercentage { get; set; }
    public double FuelEfficiency { get; set; }

    // Vehicle Usage
    public int UniqueVehiclesUsed { get; set; }
    public string? MostUsedVehiclePlate { get; set; }

    // Performance Rating
    public string PerformanceRating { get; set; } = string.Empty; // Excellent, Good, Average, Poor
    public string? Remarks { get; set; }

    // Navigation property
    public Driver Driver { get; set; } = null!;
}

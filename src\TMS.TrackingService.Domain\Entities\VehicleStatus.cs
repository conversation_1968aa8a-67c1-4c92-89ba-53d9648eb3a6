using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Vehicle GPS status entity (real-time and historical)
/// </summary>
public class VehicleStatus : AuditableEntity<Guid>
{
    public Guid VehicleId { get; set; }
    public DateTime GpsTime { get; set; }
    public DateTime SysTime { get; set; }

    /// <summary>
    /// Longitude
    /// </summary>
    public double X { get; set; }

    /// <summary>
    /// Latitude
    /// </summary>
    public double Y { get; set; }

    /// <summary>
    /// Vehicle status flags
    /// 0x1: Engine On
    /// 0x2: Door Open
    /// 0x4: Air Conditioning
    /// 0x8: Passenger
    /// 0x10: SOS
    /// </summary>
    public int Status { get; set; }

    public int Speed { get; set; }
    public int Heading { get; set; }
    public int? EventId { get; set; }
    public long Distance { get; set; }
    public string? DriverName { get; set; }
    public string? LicenseNo { get; set; }
    public string? Address { get; set; }

    /// <summary>
    /// Sensor data stored as JSON
    /// </summary>
    public string? SensorsJson { get; set; }

    public string? ContainerId { get; set; }
    public string? ContainerName { get; set; }
    public string? ContainerSerial { get; set; }

    // Navigation property
    public Vehicle Vehicle { get; set; } = null!;
}

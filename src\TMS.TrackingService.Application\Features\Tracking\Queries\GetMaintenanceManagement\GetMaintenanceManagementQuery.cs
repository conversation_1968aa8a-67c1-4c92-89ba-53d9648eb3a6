using MediatR;
using TMS.TrackingService.Contracts.Vietmap;

namespace TMS.TrackingService.Application.Features.Tracking.Queries.GetMaintenanceManagement;

public class GetMaintenanceManagementQuery : IRequest<MaintenanceManagementResponse>
{
    public string? Plate { get; set; }
    public int? VehicleGroupId { get; set; }
    public int? Type { get; set; }
    public int? Page { get; set; }
    public int? PageSize { get; set; }
}

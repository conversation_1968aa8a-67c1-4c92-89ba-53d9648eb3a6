namespace TMS.TrackingService.Contracts.Tracking;

public record GpsLocationRequest
{
    public string DeviceId { get; init; } = string.Empty;
    public string? VehiclePlate { get; init; }
    public double Latitude { get; init; }
    public double Longitude { get; init; }
    public double? Altitude { get; init; }
    public double? Speed { get; init; }
    public double? Heading { get; init; }
    public double? Accuracy { get; init; }
    public DateTime Timestamp { get; init; }
    public int? BatteryLevel { get; init; }
}

﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data.Configurations;

public class VehicleTripConfiguration : IEntityTypeConfiguration<VehicleTrip>
{
    public void Configure(EntityTypeBuilder<VehicleTrip> builder)
    {
        builder.ToTable("VehicleTrips");

        builder.HasKey(vt => vt.Id);

        builder.Property(vt => vt.FromAddress).HasMaxLength(500);
        builder.Property(vt => vt.ToAddress).HasMaxLength(500);
        builder.Property(vt => vt.DriverName).HasMaxLength(200);
        builder.Property(vt => vt.LicenseNo).HasMaxLength(50);

        builder.HasIndex(vt => vt.VehicleId);
        builder.HasIndex(vt => vt.FromTime);
        builder.HasIndex(vt => new { vt.VehicleId, vt.FromTime });

        builder.HasOne(vt => vt.Vehicle)
            .WithMany(v => v.VehicleTrips)
            .HasForeignKey(vt => vt.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(vt => vt.Driver)
            .WithMany()
            .HasForeignKey(vt => vt.DriverId)
            .OnDelete(DeleteBehavior.SetNull);
    }
}

public class TollgateConfiguration : IEntityTypeConfiguration<Tollgate>
{
    public void Configure(EntityTypeBuilder<Tollgate> builder)
    {
        builder.ToTable("Tollgates");

        builder.HasKey(t => t.Id);

        builder.Property(t => t.VietmapId).IsRequired().HasMaxLength(50);
        builder.Property(t => t.Name).IsRequired().HasMaxLength(200);
        builder.Property(t => t.Address).HasMaxLength(500);

        builder.HasIndex(t => t.VietmapId).IsUnique();
        builder.HasIndex(t => t.Name);

        builder.HasMany(t => t.TollgateReports)
            .WithOne()
            .HasForeignKey("FromTollgateId")
            .OnDelete(DeleteBehavior.Restrict);
    }
}

public class TollgateReportConfiguration : IEntityTypeConfiguration<TollgateReport>
{
    public void Configure(EntityTypeBuilder<TollgateReport> builder)
    {
        builder.ToTable("TollgateReports");

        builder.HasKey(tr => tr.Id);

        builder.Property(tr => tr.FromTollgateName).IsRequired().HasMaxLength(200);
        builder.Property(tr => tr.ToTollgateName).IsRequired().HasMaxLength(200);
        builder.Property(tr => tr.TollgatePrice).HasPrecision(18, 2);
        builder.Property(tr => tr.FromAddress).HasMaxLength(500);
        builder.Property(tr => tr.ToAddress).HasMaxLength(500);

        builder.HasIndex(tr => tr.VehicleId);
        builder.HasIndex(tr => tr.FromTime);
        builder.HasIndex(tr => new { tr.VehicleId, tr.FromTime });

        builder.HasOne(tr => tr.Vehicle)
            .WithMany()
            .HasForeignKey(tr => tr.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(tr => tr.Driver)
            .WithMany()
            .HasForeignKey(tr => tr.DriverId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(tr => tr.FromTollgate)
            .WithMany()
            .HasForeignKey(tr => tr.FromTollgateId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(tr => tr.ToTollgate)
            .WithMany()
            .HasForeignKey(tr => tr.ToTollgateId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}

public class TelematicsViolationConfiguration : IEntityTypeConfiguration<TelematicsViolation>
{
    public void Configure(EntityTypeBuilder<TelematicsViolation> builder)
    {
        builder.ToTable("TelematicsViolations");

        builder.HasKey(tv => tv.Id);

        builder.Property(tv => tv.BehaviorType).IsRequired().HasMaxLength(50);
        builder.Property(tv => tv.Address).HasMaxLength(500);

        builder.HasIndex(tv => tv.VehicleId);
        builder.HasIndex(tv => tv.DriverId);
        builder.HasIndex(tv => tv.ViolationTime);
        builder.HasIndex(tv => tv.BehaviorType);

        builder.HasOne(tv => tv.Vehicle)
            .WithMany()
            .HasForeignKey(tv => tv.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(tv => tv.Driver)
            .WithMany(d => d.TelematicsViolations)
            .HasForeignKey(tv => tv.DriverId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}

public class MaintenanceRecordConfiguration : IEntityTypeConfiguration<MaintenanceRecord>
{
    public void Configure(EntityTypeBuilder<MaintenanceRecord> builder)
    {
        builder.ToTable("MaintenanceRecords");

        builder.HasKey(mr => mr.Id);

        builder.Property(mr => mr.MaintenanceType).IsRequired().HasMaxLength(200);
        builder.Property(mr => mr.Amount).HasPrecision(18, 2);
        builder.Property(mr => mr.Remark).HasMaxLength(1000);

        builder.HasIndex(mr => mr.VehicleId);
        builder.HasIndex(mr => mr.MaintenanceTypeId);
        builder.HasIndex(mr => mr.NextDueDate);
        builder.HasIndex(mr => new { mr.VehicleId, mr.Completed });

        builder.HasOne(mr => mr.Vehicle)
            .WithMany()
            .HasForeignKey(mr => mr.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Vehicle trip entity
/// </summary>
public class VehicleTrip : AuditableEntity<Guid>
{
    public Guid VehicleId { get; set; }
    public Guid? DriverId { get; set; }
    public DateTime FromTime { get; set; }
    public DateTime ToTime { get; set; }
    public int Duration { get; set; }
    public string? FromAddress { get; set; }
    public string? ToAddress { get; set; }
    public string? DriverName { get; set; }
    public int Waypoint { get; set; }
    public int Status { get; set; }
    public double FromX { get; set; }
    public double FromY { get; set; }
    public int FromRegionId { get; set; }
    public double ToX { get; set; }
    public double ToY { get; set; }
    public int ToRegionId { get; set; }
    public long GpsDistance { get; set; }
    public int MaxSpeed { get; set; }
    public int MinSpeed { get; set; }
    public int AverageSpeed { get; set; }
    public int OverSpeed { get; set; }
    public long GpsMileage { get; set; }
    public long FromMileage { get; set; }
    public long ToMileage { get; set; }
    public int DoorOpenCount { get; set; }
    public int DoorCloseCount { get; set; }
    public bool StartAtStation { get; set; }
    public bool EndAtStation { get; set; }
    public long StopTime { get; set; }
    public long FromDistance { get; set; }
    public long ToDistance { get; set; }
    public int RestTime { get; set; }
    public string? LicenseNo { get; set; }
    public int RunTime { get; set; }

    // Navigation property
    public Vehicle Vehicle { get; set; } = null!;
    public Driver? Driver { get; set; }
}

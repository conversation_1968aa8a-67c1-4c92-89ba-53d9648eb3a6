﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data.Configurations;

public class VehicleStatusConfiguration : IEntityTypeConfiguration<VehicleStatus>
{
    public void Configure(EntityTypeBuilder<VehicleStatus> builder)
    {
        builder.ToTable("VehicleStatuses");

        builder.HasKey(vs => vs.Id);

        builder.Property(vs => vs.VehicleId)
            .IsRequired();

        builder.Property(vs => vs.GpsTime)
            .IsRequired();

        builder.Property(vs => vs.SysTime)
            .IsRequired();

        builder.Property(vs => vs.X)
            .IsRequired();

        builder.Property(vs => vs.Y)
            .IsRequired();

        builder.Property(vs => vs.DriverName)
            .HasMaxLength(200);

        builder.Property(vs => vs.LicenseNo)
            .HasMaxLength(50);

        builder.Property(vs => vs.Address)
            .HasMaxLength(500);

        builder.Property(vs => vs.SensorsJson);

        builder.Property(vs => vs.ContainerId)
            .HasMaxLength(50);

        builder.Property(vs => vs.ContainerName)
            .HasMaxLength(200);

        builder.Property(vs => vs.ContainerSerial)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(vs => vs.VehicleId);

        builder.HasIndex(vs => vs.GpsTime);

        builder.HasIndex(vs => new { vs.VehicleId, vs.GpsTime });

        // Relationship
        builder.HasOne(vs => vs.Vehicle)
            .WithMany(v => v.VehicleStatuses)
            .HasForeignKey(vs => vs.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

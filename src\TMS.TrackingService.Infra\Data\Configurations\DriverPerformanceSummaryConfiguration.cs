using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data.Configurations;

public class DriverPerformanceSummaryConfiguration : IEntityTypeConfiguration<DriverPerformanceSummary>
{
    public void Configure(EntityTypeBuilder<DriverPerformanceSummary> builder)
    {
        builder.ToTable("DriverPerformanceSummaries");

        builder.HasKey(dps => dps.Id);

        builder.Property(dps => dps.Period)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(dps => dps.PerformanceRating)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(dps => dps.MostUsedVehiclePlate)
            .HasMaxLength(20);

        builder.Property(dps => dps.Remarks)
            .HasMaxLength(500);

        // Indexes
        builder.HasIndex(dps => dps.DriverId);
        builder.HasIndex(dps => dps.SummaryDate);
        builder.HasIndex(dps => dps.Period);
        builder.HasIndex(dps => new { dps.DriverId, dps.SummaryDate, dps.Period })
            .IsUnique()
            .HasDatabaseName("IX_DriverPerformanceSummary_Unique");

        // Relationships
        builder.HasOne(dps => dps.Driver)
            .WithMany()
            .HasForeignKey(dps => dps.DriverId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Aggregated vehicle performance metrics
/// </summary>
public class VehiclePerformanceSummary : AuditableEntity<Guid>
{
    public Guid VehicleId { get; set; }
    public DateTime SummaryDate { get; set; }
    public string Period { get; set; } = string.Empty; // Daily, Weekly, Monthly

    // Distance Metrics
    public long TotalDistance { get; set; } // meters
    public double AverageDistancePerDay { get; set; }
    public long MaxDistanceInDay { get; set; }

    // Time Metrics
    public int TotalRunTime { get; set; } // seconds
    public int TotalIdleTime { get; set; } // seconds
    public int TotalStopTime { get; set; } // seconds
    public double UtilizationRate { get; set; } // percentage

    // Speed Metrics
    public int MaxSpeed { get; set; }
    public double AverageSpeed { get; set; }
    public int OverSpeedCount { get; set; }
    public int OverSpeedDuration { get; set; } // seconds

    // Fuel Metrics (if available)
    public double? TotalFuelConsumed { get; set; }
    public double? AverageFuelConsumption { get; set; } // per 100km
    public double? FuelEfficiencyScore { get; set; }

    // Trip Metrics
    public int TotalTrips { get; set; }
    public double AverageTripDistance { get; set; }
    public int AverageTripDuration { get; set; }

    // Driver Metrics
    public int UniqueDriversCount { get; set; }
    public string? MostFrequentDriver { get; set; }

    // Violation Metrics
    public int TotalViolations { get; set; }
    public int HarshAccelerationCount { get; set; }
    public int HarshBrakingCount { get; set; }
    public int OverdrivingCount { get; set; }

    // Operational Metrics
    public int DoorOpenCount { get; set; }
    public int DaysActive { get; set; }
    public DateTime? FirstActivityTime { get; set; }
    public DateTime? LastActivityTime { get; set; }

    // Cost Metrics (if configured)
    public decimal? TollgateCost { get; set; }
    public decimal? MaintenanceCost { get; set; }
    public decimal? EstimatedOperationalCost { get; set; }

    // Navigation property
    public Vehicle Vehicle { get; set; } = null!;
}

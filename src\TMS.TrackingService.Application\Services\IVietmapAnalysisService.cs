namespace TMS.TrackingService.Application.Services;

/// <summary>
/// Service for analyzing and aggregating Vietmap tracking data
/// </summary>
public interface IVietmapAnalysisService
{
    /// <summary>
    /// Calculate vehicle performance summaries for a specific period
    /// </summary>
    /// <param name="fromDate">Start date for analysis</param>
    /// <param name="toDate">End date for analysis</param>
    /// <param name="period">Period type (Daily, Weekly, Monthly)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of summaries created/updated</returns>
    Task<int> CalculateVehiclePerformanceSummariesAsync(
        DateTime fromDate,
        DateTime toDate,
        string period = "Daily",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate driver performance summaries for a specific period
    /// </summary>
    /// <param name="fromDate">Start date for analysis</param>
    /// <param name="toDate">End date for analysis</param>
    /// <param name="period">Period type (Daily, Weekly, Monthly)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of summaries created/updated</returns>
    Task<int> CalculateDriverPerformanceSummariesAsync(
        DateTime fromDate,
        DateTime toDate,
        string period = "Daily",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate all performance summaries (vehicles + drivers) for a specific date
    /// </summary>
    /// <param name="summaryDate">Date to calculate summaries for</param>
    /// <param name="period">Period type (Daily, Weekly, Monthly)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total number of summaries created/updated</returns>
    Task<int> CalculateAllPerformanceSummariesAsync(
        DateTime summaryDate,
        string period = "Daily",
        CancellationToken cancellationToken = default);
}

using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Daily vehicle report entity
/// </summary>
public class DailyReport : AuditableEntity<Guid>
{
    public Guid VehicleId { get; set; }
    public DateTime Date { get; set; }
    public long Distance { get; set; }
    public int DoorOpenCount { get; set; }
    public int OverSpeedCount { get; set; }
    public int MaxSpeed { get; set; }
    public DateTime? FirstAccOnTime { get; set; }
    public DateTime? LastAccOffTime { get; set; }
    public int AccTime { get; set; }
    public int RunTime { get; set; }
    public int IdleTime { get; set; }
    public int StopTime { get; set; }
    public DateTime SysTime { get; set; }

    // Navigation property
    public Vehicle Vehicle { get; set; } = null!;
}

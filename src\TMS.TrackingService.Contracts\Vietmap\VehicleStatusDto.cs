using System.Text.Json.Serialization;

namespace TMS.TrackingService.Contracts.Vietmap;

/// <summary>
/// API 2: Vehicle GPS status (real-time)
/// </summary>
public class VehicleStatusDto
{
    public int Id { get; set; }
    public DateTime GpsTime { get; set; }
    public DateTime SysTime { get; set; }
    public double X { get; set; }
    public double Y { get; set; }
    public int Status { get; set; }
    public int Speed { get; set; }
    public int Heading { get; set; }
    public int? EventId { get; set; }
    public long Distance { get; set; }
    public string? Driver { get; set; }
    public string? LicenseNo { get; set; }
    public List<SensorDto>? Sensors { get; set; }
    public string? Address { get; set; }
    public string? Url { get; set; }
    public string? ContainerId { get; set; }
    public string? ContainerName { get; set; }
    public string? ContainerSerial { get; set; }
}

public class SensorDto
{
    public int SensorTypeId { get; set; }
    public double Value { get; set; }
    public SensorData? Data { get; set; }
}

public class SensorData
{
    public double? T1 { get; set; }
    public double? T2 { get; set; }
    public double? T3 { get; set; }
    public double? Amount { get; set; }
    public double? Distance { get; set; }
    public double? EmptyDistance { get; set; }
    public double? Speed { get; set; }
    public int? Idle { get; set; }
    public bool? Passenger { get; set; }
}

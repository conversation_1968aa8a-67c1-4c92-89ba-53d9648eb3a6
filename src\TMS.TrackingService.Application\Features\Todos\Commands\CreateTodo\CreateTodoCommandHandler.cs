﻿using MapsterMapper;
using MediatR;
using TMS.SharedKernel.Domain;
using TMS.TrackingService.Contracts.Todos;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Application.Features.Todos.Commands.CreateTodo;

public class CreateTodoCommandHandler : IRequestHandler<CreateTodoCommand, TodoResponse>
{
    private readonly IBaseRepository<Todo> _todoRepository;
    private readonly IMapper _mapper;
    private readonly IUnitOfWork _unitOfWork;

    public CreateTodoCommandHandler(IUnitOfWork unitOfWork, IBaseRepository<Todo> todoRepository, IMapper mapper)
    {
        _todoRepository = todoRepository;
        _mapper = mapper;
        _unitOfWork = unitOfWork;
    }

    public async Task<TodoResponse> Handle(CreateTodoCommand request, CancellationToken cancellationToken)
    {
        var todo = new Todo
        {
            Id = Guid.NewGuid(),
            Title = request.Title,
            Description = request.Description,
            IsCompleted = false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _todoRepository.AddAsync(todo, cancellationToken);

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return _mapper.Map<TodoResponse>(todo);
    }
}

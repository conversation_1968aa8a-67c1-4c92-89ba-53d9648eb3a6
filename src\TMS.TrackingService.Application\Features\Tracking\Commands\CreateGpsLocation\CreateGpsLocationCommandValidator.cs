using FluentValidation;

namespace TMS.TrackingService.Application.Features.Tracking.Commands.CreateGpsLocation;

public class CreateGpsLocationCommandValidator : AbstractValidator<CreateGpsLocationCommand>
{
    public CreateGpsLocationCommandValidator()
    {
        RuleFor(x => x.DeviceId)
            .NotEmpty().WithMessage("DeviceId is required")
            .MaximumLength(100).WithMessage("DeviceId must not exceed 100 characters");

        RuleFor(x => x.Latitude)
            .InclusiveBetween(-90, 90).WithMessage("Latitude must be between -90 and 90");

        RuleFor(x => x.Longitude)
            .InclusiveBetween(-180, 180).WithMessage("Longitude must be between -180 and 180");

        RuleFor(x => x.Speed)
            .GreaterThanOrEqualTo(0).When(x => x.Speed.HasValue)
            .WithMessage("Speed must be greater than or equal to 0");

        RuleFor(x => x.Heading)
            .InclusiveBetween(0, 360).When(x => x.Heading.HasValue)
            .WithMessage("Heading must be between 0 and 360");

        RuleFor(x => x.BatteryLevel)
            .InclusiveBetween(0, 100).When(x => x.BatteryLevel.HasValue)
            .WithMessage("BatteryLevel must be between 0 and 100");

        RuleFor(x => x.Timestamp)
            .NotEmpty().WithMessage("Timestamp is required")
            .LessThanOrEqualTo(DateTime.UtcNow.AddMinutes(5))
            .WithMessage("Timestamp cannot be more than 5 minutes in the future");
    }
}

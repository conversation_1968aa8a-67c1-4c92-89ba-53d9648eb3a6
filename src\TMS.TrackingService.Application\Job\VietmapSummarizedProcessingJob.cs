using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;
using TMS.TrackingService.Application.Services;

namespace TMS.TrackingService.Application.Job;

/// <summary>
/// Quartz job for processing and analyzing Vietmap tracking data
/// Generates performance summaries for vehicles and drivers
/// </summary>
[DisallowConcurrentExecution]
public class VietmapSummarizedProcessingJob : IJob
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<VietmapSummarizedProcessingJob> _logger;

    public VietmapSummarizedProcessingJob(
        IServiceProvider serviceProvider,
        ILogger<VietmapSummarizedProcessingJob> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var instanceId = Guid.NewGuid().ToString("N")[..8];
        var cancellationToken = context.CancellationToken;

        _logger.LogInformation("====================================================");
        _logger.LogInformation("Starting Vietmap Summarized Processing Job - Instance: {InstanceId}", instanceId);
        _logger.LogInformation("====================================================");

        using var scope = _serviceProvider.CreateScope();
        var analysisService = scope.ServiceProvider.GetRequiredService<IVietmapAnalysisService>();

        try
        {
            // Get configuration from job data map
            var dataMap = context.JobDetail.JobDataMap;
            var processPeriods = dataMap.GetString("ProcessPeriods") ?? "Daily";
            var lookbackDays = dataMap.GetIntValue("LookbackDays");
            if (lookbackDays == 0) lookbackDays = 1; // Default to yesterday

            var periods = processPeriods.Split(',', StringSplitOptions.RemoveEmptyEntries);

            _logger.LogInformation("Configuration:");
            _logger.LogInformation("  - Process Periods: {Periods}", string.Join(", ", periods));
            _logger.LogInformation("  - Lookback Days: {Days}", lookbackDays);
            _logger.LogInformation("");

            var totalSummaries = 0;

            // Process each period type
            foreach (var period in periods)
            {
                var trimmedPeriod = period.Trim();
                _logger.LogInformation("--- Processing {Period} Summaries ---", trimmedPeriod);

                try
                {
                    // Calculate the date range based on period type
                    var summaryDate = DateTime.UtcNow.Date.AddDays(-lookbackDays);

                    _logger.LogInformation("Calculating summaries for date: {SummaryDate:yyyy-MM-dd}", summaryDate);

                    var count = await analysisService.CalculateAllPerformanceSummariesAsync(
                        summaryDate,
                        trimmedPeriod,
                        cancellationToken);

                    totalSummaries += count;

                    _logger.LogInformation("✓ Processed {Count} {Period} summaries", count, trimmedPeriod);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing {Period} summaries", trimmedPeriod);
                }

                _logger.LogInformation("");
            }

            // Additional processing: Calculate weekly summaries if today is Monday and configured
            if (periods.Contains("Weekly", StringComparer.OrdinalIgnoreCase) &&
                DateTime.UtcNow.DayOfWeek == DayOfWeek.Monday)
            {
                _logger.LogInformation("--- Processing Weekly Summaries (Monday) ---");

                try
                {
                    var lastWeekStart = DateTime.UtcNow.Date.AddDays(-7);

                    var count = await analysisService.CalculateAllPerformanceSummariesAsync(
                        lastWeekStart,
                        "Weekly",
                        cancellationToken);

                    totalSummaries += count;

                    _logger.LogInformation("✓ Processed {Count} weekly summaries for last week", count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing weekly summaries");
                }

                _logger.LogInformation("");
            }

            // Additional processing: Calculate monthly summaries if today is 1st day and configured
            if (periods.Contains("Monthly", StringComparer.OrdinalIgnoreCase) &&
                DateTime.UtcNow.Day == 1)
            {
                _logger.LogInformation("--- Processing Monthly Summaries (1st of Month) ---");

                try
                {
                    var lastMonthStart = DateTime.UtcNow.Date.AddMonths(-1);

                    var count = await analysisService.CalculateAllPerformanceSummariesAsync(
                        lastMonthStart,
                        "Monthly",
                        cancellationToken);

                    totalSummaries += count;

                    _logger.LogInformation("✓ Processed {Count} monthly summaries for last month", count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing monthly summaries");
                }

                _logger.LogInformation("");
            }

            _logger.LogInformation("====================================================");
            _logger.LogInformation("Vietmap Summarized Processing Job Completed - Instance: {InstanceId}", instanceId);
            _logger.LogInformation("Total Summaries Processed: {TotalSummaries}", totalSummaries);
            _logger.LogInformation("====================================================");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in Vietmap Summarized Processing Job - Instance: {InstanceId}", instanceId);
            throw;
        }
    }
}

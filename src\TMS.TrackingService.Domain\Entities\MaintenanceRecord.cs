using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Vehicle maintenance record entity
/// </summary>
public class MaintenanceRecord : AuditableEntity<Guid>
{
    public Guid VehicleId { get; set; }
    public int MaintenanceTypeId { get; set; }
    public string MaintenanceType { get; set; } = string.Empty;
    public DateTime? LastPerformed { get; set; }
    public DateTime? NextDueDate { get; set; }
    public long DueDistance { get; set; }
    public long ActualDistance { get; set; }
    public DateTime? ActualDistanceDate { get; set; }
    public long LastMileage { get; set; }
    public bool Completed { get; set; }
    public DateTime? CompletedDate { get; set; }
    public DateTime? DateWarning { get; set; }
    public long DistanceWarning { get; set; }
    public decimal Amount { get; set; }
    public string? Remark { get; set; }
    public bool Inactive { get; set; }

    // Navigation property
    public Vehicle Vehicle { get; set; } = null!;
}

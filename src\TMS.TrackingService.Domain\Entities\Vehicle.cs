using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Vehicle entity for persistence
/// </summary>
public class Vehicle : AuditableEntity<Guid>
{
    public int VietmapId { get; set; }
    public string Plate { get; set; } = string.Empty;
    public string ActualPlate { get; set; } = string.Empty;
    public int GroupId { get; set; }
    public string GroupName { get; set; } = string.Empty;
    public int VehicleTypeId { get; set; }
    public string VehicleTypeName { get; set; } = string.Empty;
    public double Capacity { get; set; }
    public string? Vin { get; set; }
    public string? BrandName { get; set; }
    public string? ProductYear { get; set; }
    public DateTime? RegisterDate { get; set; }
    public double? MaxSpeed { get; set; }
    public double FuelPer100km { get; set; }
    public double FuelPerIdleHour { get; set; }
    public double FuelPerIgnitionHour { get; set; }
    public int EmissionsHour { get; set; }

    // Navigation properties
    public ICollection<VehicleStatus> VehicleStatuses { get; set; } = new List<VehicleStatus>();
    public ICollection<DailyReport> DailyReports { get; set; } = new List<DailyReport>();
    public ICollection<VehicleTrip> VehicleTrips { get; set; } = new List<VehicleTrip>();
}

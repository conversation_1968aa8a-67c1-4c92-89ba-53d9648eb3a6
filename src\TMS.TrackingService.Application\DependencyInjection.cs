﻿using System.Reflection;
using FluentValidation;
using Mapster;
using MapsterMapper;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using TMS.TrackingService.Application.Common.Behaviors;
using TMS.TrackingService.Application.Services;

namespace TMS.TrackingService.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();

        // MediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssembly(assembly);
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
        });

        // FluentValidation
        services.AddValidatorsFromAssembly(assembly);

        // Mapster
        // Use the global instance of TypeAdapterConfig. Creating a 'new TypeAdapterConfig()'
        // here would result in an empty configuration for the mapper.
        var config = TypeAdapterConfig.GlobalSettings;
        // This is a more robust way to register all your mappings that implement IRegister
        config.Scan(assembly);
        services.AddSingleton(config);
        services.AddScoped<IMapper, ServiceMapper>();

        // Application Services
        services.AddScoped<IVietmapSyncService, VietmapSyncService>();
        services.AddScoped<IVietmapAnalysisService, VietmapAnalysisService>();

        return services;
    }
}

# Vietmap Tracking API Implementation Guide

## Overview
This document provides a comprehensive implementation of all 17 Vietmap Tracking APIs with Refit interfaces, entities, database schemas, and controllers.

## Architecture

### Project Structure
```
TMS.TrackingService/
├── ApiClient/
│   └── IVietmapTrackingApi.cs         # Refit interface for Vietmap APIs
├── Contracts/
│   └── Vietmap/
│       ├── VietmapDataResponse.cs     # Common response wrapper
│       ├── VehicleDto.cs              # API 1 DTOs
│       ├── VehicleStatusDto.cs        # API 2 DTOs
│       └── VietmapDtos.cs             # API 3-17 DTOs
├── Domain/
│   └── Entities/
│       ├── Vehicle.cs                 # Vehicle entity
│       ├── VehicleStatus.cs           # GPS status entity
│       ├── DailyReport.cs             # Daily reports
│       ├── Driver.cs                  # Driver entity
│       ├── DriverShift.cs             # Driver shifts
│       ├── VehicleTrip.cs             # Trip records
│       ├── Tollgate.cs                # Tollgate master data
│       ├── TollgateReport.cs          # Tollgate reports
│       ├── TelematicsViolation.cs     # Violations
│       └── MaintenanceRecord.cs       # Maintenance records
├── Infra/
│   └── Data/
│       ├── ApplicationDbContext.cs    # EF Core DbContext
│       └── Configurations/            # Entity configurations
└── Api/
    └── Controllers/
        └── TrackingController.cs      # API endpoints
```

## API Mapping

### API 1: Get Vehicles
- **Vietmap Endpoint**: `GET /v3/tracking/getvehicles`
- **Our Endpoint**: `GET /api/v1/tracking/vehicles`
- **Entity**: `Vehicle`
- **Purpose**: Retrieve list of vehicles for user

### API 2: Get Vehicle Status (Real-time)
- **Vietmap Endpoint**: `GET /v3/tracking/GetVehicleStatus`
- **Our Endpoint**: `GET /api/v1/tracking/vehicles/{plate}/status`
- **Entity**: `VehicleStatus`
- **Purpose**: Get current GPS position and status

### API 3: Get Vehicle History
- **Vietmap Endpoint**: `GET /v3/tracking/getvehiclehistory`
- **Our Endpoint**: `GET /api/v1/tracking/vehicles/{id}/history`
- **Entity**: `VehicleStatus`
- **Purpose**: Get historical GPS data (max 3 days)

### API 4: Get Vehicle Images
- **Vietmap Endpoint**: `GET /v3/tracking/getvehicleimages`
- **Our Endpoint**: `GET /api/v1/tracking/vehicles/{id}/images`
- **Purpose**: Get vehicle images for time period (max 3 days)

### API 5: Get Daily Reports
- **Vietmap Endpoint**: `GET /v3/tracking/GetDailyReports`
- **Our Endpoint**: `GET /api/v1/tracking/reports/daily`
- **Entity**: `DailyReport`
- **Purpose**: Get daily distance and usage statistics (max 31 days)

### API 6: Get Point In/Out Reports
- **Vietmap Endpoint**: `GET /v3/tracking/GetVehiclePointReports`
- **Our Endpoint**: `GET /api/v1/tracking/reports/points`
- **Purpose**: Track vehicle entry/exit from defined points (max 31 days)

### API 7: Get Tollgate Reports
- **Vietmap Endpoint**: `GET /v3/tracking/GetVehicleTollgateReports`
- **Our Endpoint**: `GET /api/v1/tracking/reports/tollgates`
- **Entity**: `TollgateReport`
- **Purpose**: Track tollgate passages (max 31 days)

### API 8: Get Driver Shift Reports
- **Vietmap Endpoint**: `GET /v3/tracking/GetVehicleShiftReports`
- **Our Endpoint**: `GET /api/v1/tracking/reports/shifts`
- **Entity**: `DriverShift`
- **Purpose**: Track driver shifts and statistics (max 31 days)

### API 9: Get Vehicle Stop Reports
- **Vietmap Endpoint**: `GET /v3/tracking/GetVehicleStopReports`
- **Our Endpoint**: `GET /api/v1/tracking/reports/stops`
- **Purpose**: Track vehicle stops (max 31 days)

### API 10: Get Maintenance Reports
- **Vietmap Endpoint**: `GET /v3/tracking/GetVehicleMaintenanceReport`
- **Our Endpoint**: `GET /api/v1/tracking/reports/maintenance`
- **Entity**: `MaintenanceRecord`
- **Purpose**: Get vehicle maintenance history

### API 11: Get Temperature Reports
- **Vietmap Endpoint**: `GET /v3/tracking/GetVehicleTemperatureReport`
- **Our Endpoint**: `GET /api/v1/tracking/reports/temperature`
- **Purpose**: Get temperature sensor data (max 31 days)

### API 12: Get Maintenance Management
- **Vietmap Endpoint**: `GET /v3/tracking/GetMaintenanceManagement`
- **Our Endpoint**: `GET /api/v1/tracking/maintenance`
- **Entity**: `MaintenanceRecord`
- **Purpose**: Manage maintenance schedules

### API 13: Get Drivers
- **Vietmap Endpoint**: `GET /v3/tracking/GetDrivers`
- **Our Endpoint**: `GET /api/v1/tracking/drivers`
- **Entity**: `Driver`
- **Purpose**: Get list of drivers

### API 14: Get Fuel Reports
- **Vietmap Endpoint**: `GET /v3/tracking/GetVehicleFuelReport`
- **Our Endpoint**: `GET /api/v1/tracking/reports/fuel`
- **Purpose**: Get fuel consumption by sensor

### API 15: Get Trip Reports
- **Vietmap Endpoint**: `GET /v3/tracking/GetVehicleTripReport`
- **Our Endpoint**: `GET /api/v1/tracking/reports/trips`
- **Entity**: `VehicleTrip`
- **Purpose**: Get trip details (max 31 days)

### API 16: Get Tollgates
- **Vietmap Endpoint**: `GET /v3/tracking/gettollgates`
- **Our Endpoint**: `GET /api/v1/tracking/tollgates`
- **Entity**: `Tollgate`
- **Purpose**: Get list of tollgates

### API 17: Get Telematics Violations
- **Vietmap Endpoint**: `GET /v3/tracking/gettelematicsviolation`
- **Our Endpoint**: `GET /api/v1/tracking/reports/violations`
- **Entity**: `TelematicsViolation`
- **Purpose**: Get driver violations (overspeed, harsh braking, etc.)

## Database Schema

### Core Tables

#### Vehicles
```sql
- Id (Guid, PK)
- VietmapId (int, unique)
- Plate (nvarchar(20))
- ActualPlate (nvarchar(20))
- GroupId (int)
- GroupName (nvarchar(200))
- VehicleTypeId (int)
- VehicleTypeName (nvarchar(100))
- Capacity (float)
- Vin (nvarchar(50))
- BrandName (nvarchar(100))
- ProductYear (nvarchar(10))
- RegisterDate (datetime2)
- MaxSpeed (float)
- FuelPer100km (float)
- FuelPerIdleHour (float)
- FuelPerIgnitionHour (float)
- EmissionsHour (int)
- Created/Updated/CreatedBy/UpdatedBy (audit fields)
```

#### VehicleStatuses
```sql
- Id (Guid, PK)
- VehicleId (Guid, FK -> Vehicles)
- GpsTime (datetime2)
- SysTime (datetime2)
- X (float) - Longitude
- Y (float) - Latitude
- Status (int) - Bitflags: 0x1=Engine, 0x2=Door, 0x4=AC, 0x8=Passenger, 0x10=SOS
- Speed (int)
- Heading (int)
- EventId (int, nullable)
- Distance (bigint)
- DriverName (nvarchar(200))
- LicenseNo (nvarchar(50))
- Address (nvarchar(500))
- SensorsJson (nvarchar(max)) - JSON
- ContainerId (nvarchar(50))
- ContainerName (nvarchar(200))
- ContainerSerial (nvarchar(100))
- Created/Updated/CreatedBy/UpdatedBy
```

#### DailyReports
```sql
- Id (Guid, PK)
- VehicleId (Guid, FK -> Vehicles)
- Date (datetime2, unique per vehicle)
- Distance (bigint)
- DoorOpenCount (int)
- OverSpeedCount (int)
- MaxSpeed (int)
- FirstAccOnTime (datetime2)
- LastAccOffTime (datetime2)
- AccTime (int) - seconds
- RunTime (int) - seconds
- IdleTime (int) - seconds
- StopTime (int) - seconds
- SysTime (datetime2)
- Created/Updated/CreatedBy/UpdatedBy
```

#### Drivers
```sql
- Id (Guid, PK)
- VietmapId (int, unique)
- Code (nvarchar(50))
- Rfid (nvarchar(50))
- Name (nvarchar(200))
- Phone (nvarchar(20))
- Email (nvarchar(100))
- Address (nvarchar(500))
- LicenseNo (nvarchar(50))
- LicenseExpireDate (datetime2)
- StartDate (datetime2)
- EndDate (datetime2)
- Remark (nvarchar(1000))
- Created/Updated/CreatedBy/UpdatedBy
```

#### DriverShifts
```sql
- Id (Guid, PK)
- VehicleId (Guid, FK -> Vehicles)
- DriverId (Guid, FK -> Drivers)
- Plate (nvarchar(20))
- DriverName (nvarchar(200))
- LicenseNo (nvarchar(50))
- IdNo (nvarchar(50))
- FromTime (datetime2)
- ToTime (datetime2)
- FromAddress (nvarchar(500))
- ToAddress (nvarchar(500))
- Distance (bigint)
- DoorOpenCount (int)
- DoorCloseCount (int)
- OverSpeedCount (int)
- MaxSpeed (int)
- AccTime (int)
- IdleTime (int)
- RunTime (int)
- StopTime (int)
- Created/Updated/CreatedBy/UpdatedBy
```

#### VehicleTrips
```sql
- Id (Guid, PK)
- VehicleId (Guid, FK -> Vehicles)
- DriverId (Guid, FK -> Drivers, nullable)
- FromTime (datetime2)
- ToTime (datetime2)
- Duration (int)
- FromAddress (nvarchar(500))
- ToAddress (nvarchar(500))
- DriverName (nvarchar(200))
- Waypoint (int)
- Status (int)
- FromX, FromY, ToX, ToY (float)
- FromRegionId, ToRegionId (int)
- GpsDistance (bigint)
- MaxSpeed, MinSpeed, AverageSpeed (int)
- OverSpeed (int)
- GpsMileage (bigint)
- FromMileage, ToMileage (bigint)
- DoorOpenCount, DoorCloseCount (int)
- StartAtStation, EndAtStation (bit)
- StopTime, FromDistance, ToDistance (bigint)
- RestTime, RunTime (int)
- LicenseNo (nvarchar(50))
- Created/Updated/CreatedBy/UpdatedBy
```

#### Tollgates
```sql
- Id (Guid, PK)
- VietmapId (nvarchar(50), unique)
- Name (nvarchar(200))
- Address (nvarchar(500))
- X (float) - Longitude
- Y (float) - Latitude
- Heading (int)
- Created/Updated/CreatedBy/UpdatedBy
```

#### TollgateReports
```sql
- Id (Guid, PK)
- VehicleId (Guid, FK -> Vehicles)
- DriverId (Guid, FK -> Drivers, nullable)
- FromTollgateId (Guid, FK -> Tollgates, nullable)
- ToTollgateId (Guid, FK -> Tollgates, nullable)
- FromTime (datetime2)
- ToTime (datetime2)
- Duration (int)
- FromTollgateName (nvarchar(200))
- ToTollgateName (nvarchar(200))
- TollgatePrice (decimal(18,2))
- FromAddress (nvarchar(500))
- ToAddress (nvarchar(500))
- Created/Updated/CreatedBy/UpdatedBy
```

#### TelematicsViolations
```sql
- Id (Guid, PK)
- VehicleId (Guid, FK -> Vehicles)
- DriverId (Guid, FK -> Drivers)
- ViolationTime (datetime2)
- BehaviorType (nvarchar(50))
  Values: HARSH_ACCELERATION, HARSH_BREAK, OVERSPEED, OVERDRIVING4H, OVERDRIVING10H
- Address (nvarchar(500))
- Longitude (float)
- Latitude (float)
- BehaviorDescriptionJson (nvarchar(max)) - JSON
- Created/Updated/CreatedBy/UpdatedBy
```

#### MaintenanceRecords
```sql
- Id (Guid, PK)
- VehicleId (Guid, FK -> Vehicles)
- MaintenanceTypeId (int)
- MaintenanceType (nvarchar(200))
- LastPerformed (datetime2)
- NextDueDate (datetime2)
- DueDistance (bigint)
- ActualDistance (bigint)
- ActualDistanceDate (datetime2)
- LastMileage (bigint)
- Completed (bit)
- CompletedDate (datetime2)
- DateWarning (datetime2)
- DistanceWarning (bigint)
- Amount (decimal(18,2))
- Remark (nvarchar(1000))
- Inactive (bit)
- Created/Updated/CreatedBy/UpdatedBy
```

## Indexes

### VehicleStatuses
- `IX_VehicleStatuses_VehicleId`
- `IX_VehicleStatuses_GpsTime`
- `IX_VehicleStatuses_VehicleId_GpsTime` (composite)

### DailyReports
- `IX_DailyReports_VehicleId_Date` (unique composite)
- `IX_DailyReports_Date`

### DriverShifts
- `IX_DriverShifts_VehicleId_DriverId_FromTime` (composite)
- `IX_DriverShifts_FromTime`

### VehicleTrips
- `IX_VehicleTrips_VehicleId_FromTime` (composite)
- `IX_VehicleTrips_FromTime`

### TollgateReports
- `IX_TollgateReports_VehicleId_FromTime` (composite)

### TelematicsViolations
- `IX_TelematicsViolations_VehicleId`
- `IX_TelematicsViolations_DriverId`
- `IX_TelematicsViolations_ViolationTime`
- `IX_TelematicsViolations_BehaviorType`

## Usage Examples

### 1. Get All Vehicles
```http
GET /api/v1/tracking/vehicles?apikey=YOUR_API_KEY
```

### 2. Get Vehicle Real-time Status
```http
GET /api/v1/tracking/vehicles/29LD-000.00/status?apikey=YOUR_API_KEY
```

### 3. Get Vehicle History
```http
GET /api/v1/tracking/vehicles/1939/history
    ?from=20250101000000
    &to=20250103235959
    &apikey=YOUR_API_KEY
```

### 4. Get Daily Reports
```http
GET /api/v1/tracking/reports/daily
    ?id=1939
    &from=20250101000000
    &to=20250131235959
    &apikey=YOUR_API_KEY
```

### 5. Get Telematics Violations
```http
GET /api/v1/tracking/reports/violations
    ?apikey=YOUR_API_KEY
    &driverIds=1234,5678
    &from=20250101
    &to=20250131
```

## Next Steps

1. **Configure Refit in DI**
   ```csharp
   services.AddRefitClient<IVietmapTrackingApi>()
       .ConfigureHttpClient(c =>
           c.BaseAddress = new Uri("https://client-api.quanlyxe.vn"));
   ```

2. **Create Migration**
   ```bash
   dotnet ef migrations add AddVietmapTrackingEntities
   dotnet ef database update
   ```

3. **Implement Background Services**
   - Sync vehicles from Vietmap API
   - Sync drivers from Vietmap API
   - Poll and store vehicle status data
   - Generate daily reports

4. **Add Caching**
   - Cache vehicle list (refresh hourly)
   - Cache driver list (refresh hourly)
   - Cache tollgate list (refresh daily)

5. **Add Query Handlers**
   - Implement CQRS handlers for local queries
   - Add pagination support
   - Add filtering and sorting

## Notes

- All time parameters use format: `yyyyMMddHHmmss` (UTC+7)
- Maximum date ranges:
  - History/Images: 3 days
  - Reports: 31 days
- Sensor data and behavior descriptions stored as JSON for flexibility
- All entities include audit fields (Created, Updated, CreatedBy, UpdatedBy)

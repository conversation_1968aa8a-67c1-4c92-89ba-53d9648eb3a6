namespace TMS.TrackingService.Contracts.Vietmap;

/// <summary>
/// API 3: Vehicle GPS history
/// </summary>
public class VehicleHistoryDto
{
    public int Id { get; set; }
    public DateTime GpsTime { get; set; }
    public DateTime SysTime { get; set; }
    public double X { get; set; }
    public double Y { get; set; }
    public int Status { get; set; }
    public int Speed { get; set; }
    public int Heading { get; set; }
    public string? DriverName { get; set; }
    public string? LicenseNo { get; set; }
    public string? Address { get; set; }
    public List<SensorDto>? Sensors { get; set; }
    public long Distance { get; set; }
}

/// <summary>
/// API 4: Vehicle images
/// </summary>
public class VehicleImageDto
{
    public int Id { get; set; }
    public DateTime Time { get; set; }
    public int Idx { get; set; }
    public double X { get; set; }
    public double Y { get; set; }
    public string? Address { get; set; }
    public string Url { get; set; } = string.Empty;
}

/// <summary>
/// API 5: Daily reports
/// </summary>
public class DailyReportDto
{
    public int VehicleId { get; set; }
    public DateTime Date { get; set; }
    public long Distance { get; set; }
    public int DoorOpenCount { get; set; }
    public int OverSpeedCount { get; set; }
    public int MaxSpeed { get; set; }
    public DateTime? FirstAccOnTime { get; set; }
    public DateTime? LastAccOffTime { get; set; }
    public int AccTime { get; set; }
    public int RunTime { get; set; }
    public int IdleTime { get; set; }
    public int StopTime { get; set; }
    public DateTime SysTime { get; set; }
}

/// <summary>
/// API 6: Point in/out reports
/// </summary>
public class VehiclePointReportDto
{
    public int VehicleId { get; set; }
    public string Group { get; set; } = string.Empty;
    public string Plate { get; set; } = string.Empty;
    public string? Driver { get; set; }
    public DateTime FromTime { get; set; }
    public DateTime ToTime { get; set; }
    public string Point { get; set; } = string.Empty;
    public double PointX { get; set; }
    public double PointY { get; set; }
    public int PointRadius { get; set; }
    public string? From { get; set; }
    public string? To { get; set; }
}

/// <summary>
/// API 7: Tollgate in/out reports
/// </summary>
public class VehicleTollgateReportDto
{
    public int VehicleId { get; set; }
    public string Group { get; set; } = string.Empty;
    public string Plate { get; set; } = string.Empty;
    public string? Driver { get; set; }
    public DateTime FromTime { get; set; }
    public DateTime ToTime { get; set; }
    public int Duration { get; set; }
    public string FromTollgateId { get; set; } = string.Empty;
    public string FromTollgate { get; set; } = string.Empty;
    public string ToTollgateId { get; set; } = string.Empty;
    public string ToTollgate { get; set; } = string.Empty;
    public decimal TollgatePrice { get; set; }
    public string? FromAddress { get; set; }
    public string? ToAddress { get; set; }
}

/// <summary>
/// API 8: Driver shift reports
/// </summary>
public class VehicleShiftReportDto
{
    public int VehicleId { get; set; }
    public string Group { get; set; } = string.Empty;
    public string Plate { get; set; } = string.Empty;
    public string? Driver { get; set; }
    public int? DriverId { get; set; }
    public string? LicenseNo { get; set; }
    public string? IdNo { get; set; }
    public DateTime FromTime { get; set; }
    public DateTime ToTime { get; set; }
    public string? From { get; set; }
    public string? To { get; set; }
    public long Distance { get; set; }
    public int DoorOpenCount { get; set; }
    public int DoorCloseCount { get; set; }
    public int OverSpeedCount { get; set; }
    public int MaxSpeed { get; set; }
    public int AccTime { get; set; }
    public int IdleTime { get; set; }
    public int RunTime { get; set; }
    public int StopTime { get; set; }
}

/// <summary>
/// API 9: Vehicle stop reports
/// </summary>
public class VehicleStopReportDto
{
    public int VehicleId { get; set; }
    public string Group { get; set; } = string.Empty;
    public string Plate { get; set; } = string.Empty;
    public string? IdNo { get; set; }
    public string? Driver { get; set; }
    public DateTime FromTime { get; set; }
    public DateTime ToTime { get; set; }
    public string? Address { get; set; }
    public double X { get; set; }
    public double Y { get; set; }
    public int Duration { get; set; }
    public int AccTime { get; set; }
    public string? Station { get; set; }
}

/// <summary>
/// API 10: Maintenance reports
/// </summary>
public class VehicleMaintenanceReportDto
{
    public string Group { get; set; } = string.Empty;
    public int VehicleId { get; set; }
    public string Plate { get; set; } = string.Empty;
    public int MaintenceTypeId { get; set; }
    public string MaintenceType { get; set; } = string.Empty;
    public DateTime? LastPerformed { get; set; }
    public DateTime? NextDueDate { get; set; }
    public long DueDistance { get; set; }
    public string? Remark { get; set; }
    public decimal Amount { get; set; }
    public DateTime? CompletedDate { get; set; }
}

/// <summary>
/// API 11: Temperature sensor reports
/// </summary>
public class VehicleTemperatureReportDto
{
    public int VehicleId { get; set; }
    public long GpsTime { get; set; }
    public string? GpsTimeStr { get; set; }
    public long Mile { get; set; }
    public double Value { get; set; }
    public int InputValue { get; set; }
    public int Speed { get; set; }
    public double X { get; set; }
    public double Y { get; set; }
    public string? Address { get; set; }
    public string Plate { get; set; } = string.Empty;
    public int VehicleGroupId { get; set; }
    public string VehicleGroup { get; set; } = string.Empty;
    public int? DriverId { get; set; }
    public string? Driver { get; set; }
    public string? LicenseNo { get; set; }
    public double? T1 { get; set; }
    public double? T2 { get; set; }
    public int? RegionId { get; set; }
    public string? Info { get; set; }
}

/// <summary>
/// API 12: Maintenance management response
/// </summary>
public class MaintenanceManagementResponse
{
    public List<MaintenanceDto> Data { get; set; } = new();
    public int Total { get; set; }
}

public class MaintenanceDto
{
    public decimal Amount { get; set; }
    public string? Remark { get; set; }
    public int Id { get; set; }
    public int VehicleId { get; set; }
    public string Plate { get; set; } = string.Empty;
    public int MaintenTypeId { get; set; }
    public string MaintenType { get; set; } = string.Empty;
    public bool Inactive { get; set; }
    public DateTime? LastModified { get; set; }
    public DateTime? LastPerformed { get; set; }
    public DateTime? NextDueDate { get; set; }
    public long DueDistance { get; set; }
    public long ActualDistance { get; set; }
    public DateTime? ActualDistanceDate { get; set; }
    public long LastMileage { get; set; }
    public bool Completed { get; set; }
    public DateTime? CompletedDate { get; set; }
    public DateTime? DateWarning { get; set; }
    public long DistanceWarning { get; set; }
}

/// <summary>
/// API 13: Driver information
/// </summary>
public class DriverDto
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string? Rfid { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string? Address { get; set; }
    public string? LicenseNo { get; set; }
    public DateTime? LicenseExpireDate { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Remark { get; set; }
}

/// <summary>
/// API 14: Fuel reports
/// </summary>
public class VehicleFuelReportDto
{
    public string Id { get; set; } = string.Empty;
    public int VehicleId { get; set; }
    public int VehicleGroupId { get; set; }
    public string VehicleGroup { get; set; } = string.Empty;
    public string Plate { get; set; } = string.Empty;
    public DateTime Time { get; set; }
    public double GpsMileage { get; set; }
    public DateTime? FirstAccOnTime { get; set; }
    public DateTime? LastAccOffTime { get; set; }
    public int MaxSpeed { get; set; }
    public int AverageSpeed { get; set; }
    public double FuelPer100Km { get; set; }
    public int OverSpeed { get; set; }
    public int AccTime { get; set; }
    public int IdleTime { get; set; }
    public double FuelIdle { get; set; }
    public int StopTime { get; set; }
    public int StopCount { get; set; }
    public int DoorOpenCount { get; set; }
    public double FuelIgnition { get; set; }
    public int FuelSignal { get; set; }
    public double StartFuel { get; set; }
    public double EndFuel { get; set; }
    public double FuelIn { get; set; }
    public double FuelOut { get; set; }
    public double FuelConsumed { get; set; }
    public double ActualFuelIn { get; set; }
    public double ExceedFuelValue { get; set; }
    public double ExceedFuelPercent { get; set; }
    public string? Remark { get; set; }
}

/// <summary>
/// API 15: Trip reports
/// </summary>
public class VehicleTripReportDto
{
    public long FromTime { get; set; }
    public long ToTime { get; set; }
    public int Duration { get; set; }
    public string? From { get; set; }
    public string? To { get; set; }
    public string? Driver { get; set; }
    public int Waypoint { get; set; }
    public int Index { get; set; }
    public int Status { get; set; }
    public double FromX { get; set; }
    public double FromY { get; set; }
    public int FromRegionId { get; set; }
    public double ToX { get; set; }
    public double ToY { get; set; }
    public int ToRegionId { get; set; }
    public int? DriverId { get; set; }
    public int VehicleId { get; set; }
    public long GpsDistance { get; set; }
    public int MaxSpeed { get; set; }
    public int MinSpeed { get; set; }
    public int AverageSpeed { get; set; }
    public int OverSpeed { get; set; }
    public long GpsMileage { get; set; }
    public int CompanyId { get; set; }
    public long FromMileage { get; set; }
    public long ToMileage { get; set; }
    public int DoorOpenCount { get; set; }
    public int DoorCloseCount { get; set; }
    public bool StartAtStation { get; set; }
    public bool EndAtStation { get; set; }
    public long StopTime { get; set; }
    public long FromDistance { get; set; }
    public long ToDistance { get; set; }
    public int RestTime { get; set; }
    public string? LicenseNo { get; set; }
    public string Plate { get; set; } = string.Empty;
    public int VehicleGroupId { get; set; }
    public string VehicleGroup { get; set; } = string.Empty;
    public int RunTime { get; set; }
    public string? FromTimeStr { get; set; }
    public string? ToTimeStr { get; set; }
}

/// <summary>
/// API 16: Tollgate information
/// </summary>
public class TollgateDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public double X { get; set; }
    public double Y { get; set; }
    public int Heading { get; set; }
}

/// <summary>
/// API 17: Telematics violations
/// </summary>
public class TelematicsViolationDto
{
    public int VehicleGroupId { get; set; }
    public string GroupName { get; set; } = string.Empty;
    public string Plate { get; set; } = string.Empty;
    public int VehicleId { get; set; }
    public int DriverId { get; set; }
    public string DriverName { get; set; } = string.Empty;
    public string DrivingLicense { get; set; } = string.Empty;
    public DateTime DateTime { get; set; }
    public string BehaviorType { get; set; } = string.Empty;
    public string? Address { get; set; }
    public BehaviorDescriptionDto? BehaviorDescription { get; set; }
}

public class BehaviorDescriptionDto
{
    public int? FromSpeed { get; set; }
    public int? ToSpeed { get; set; }
    public int? Speed { get; set; }
    public int? MaxSpeed { get; set; }
}

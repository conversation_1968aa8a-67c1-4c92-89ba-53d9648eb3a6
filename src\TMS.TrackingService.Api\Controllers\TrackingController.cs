using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.TrackingService.Application.Features.Tracking.Commands.CreateGpsLocation;
using TMS.TrackingService.Application.Features.Tracking.Queries;
using TMS.TrackingService.Application.Features.Tracking.Queries.GetVehicles;
using TMS.TrackingService.Application.Features.Tracking.Queries.GetVehicleHistory;
using TMS.TrackingService.Application.Features.Tracking.Queries.GetDrivers;
using TMS.TrackingService.Application.Features.Tracking.Queries.GetTollgates;
using TMS.TrackingService.Application.Features.Tracking.Queries.GetDailyReports;
using TMS.TrackingService.Application.Features.Tracking.Queries.GetVehicleTripReports;
using TMS.TrackingService.Application.Features.Tracking.Queries.GetMaintenanceManagement;
using TMS.TrackingService.Contracts.Tracking;
using TMS.TrackingService.Contracts.Vietmap;

namespace TMS.TrackingService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/tracking")]
[Produces("application/json")]
public class TrackingController : ControllerBase
{
    private readonly IMediator _mediator;

    public TrackingController(IMediator mediator)
    {
        _mediator = mediator;
    }

    #region Vehicle Endpoints

    /// <summary>
    /// API 1: Get list of vehicles for user
    /// </summary>
    [HttpGet("vehicles")]
    [ProducesResponseType(typeof(List<VehicleDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<VehicleDto>>> GetVehicles()
    {
        var query = new GetVehiclesQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// API 2: Get vehicle GPS status (real-time)
    /// </summary>
    [HttpGet("vehicles/{plate}/status")]
    [ProducesResponseType(typeof(VehicleStatusResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<VehicleStatusResponse>> GetVehicleStatus(string plate)
    {
        var query = new GetVehicleStatusQuery(plate);
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// API 3: Get vehicle GPS history for a time period
    /// </summary>
    [HttpGet("vehicles/{id}/history")]
    [ProducesResponseType(typeof(List<VehicleHistoryDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<VehicleHistoryDto>>> GetVehicleHistory(
        int id,
        [FromQuery] string from,
        [FromQuery] string to)
    {
        var query = new GetVehicleHistoryQuery
        {
            VietmapVehicleId = id,
            From = from,
            To = to
        };
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    #endregion

    #region Report Endpoints

    /// <summary>
    /// API 5: Get daily distance report
    /// </summary>
    [HttpGet("reports/daily")]
    [ProducesResponseType(typeof(List<DailyReportDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<DailyReportDto>>> GetDailyReports(
        [FromQuery] int id,
        [FromQuery] string from,
        [FromQuery] string to)
    {
        var query = new GetDailyReportsQuery
        {
            VietmapVehicleId = id,
            From = from,
            To = to
        };
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// API 15: Get trip report
    /// </summary>
    [HttpGet("reports/trips")]
    [ProducesResponseType(typeof(List<VehicleTripReportDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<VehicleTripReportDto>>> GetVehicleTripReport(
        [FromQuery] string vehicles,
        [FromQuery] string from,
        [FromQuery] string to)
    {
        var query = new GetVehicleTripReportsQuery
        {
            Vehicles = vehicles,
            From = from,
            To = to
        };
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    #endregion

    #region Maintenance and Drivers

    /// <summary>
    /// API 12: Get maintenance management
    /// </summary>
    [HttpGet("maintenance")]
    [ProducesResponseType(typeof(MaintenanceManagementResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<MaintenanceManagementResponse>> GetMaintenanceManagement(
        [FromQuery] string? plate = null,
        [FromQuery] int? vehicleGroupId = null,
        [FromQuery] int? type = null,
        [FromQuery] int? page = null,
        [FromQuery] int? pageSize = null)
    {
        var query = new GetMaintenanceManagementQuery
        {
            Plate = plate,
            VehicleGroupId = vehicleGroupId,
            Type = type,
            Page = page,
            PageSize = pageSize
        };
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// API 13: Get list of drivers
    /// </summary>
    [HttpGet("drivers")]
    [ProducesResponseType(typeof(List<DriverDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<DriverDto>>> GetDrivers()
    {
        var query = new GetDriversQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// API 16: Get list of tollgates
    /// </summary>
    [HttpGet("tollgates")]
    [ProducesResponseType(typeof(List<TollgateDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<TollgateDto>>> GetTollgates()
    {
        var query = new GetTollgatesQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    #endregion

    #region GPS Location (Internal)

    /// <summary>
    /// Post GPS location data (internal endpoint)
    /// </summary>
    [HttpPost("gps")]
    [ProducesResponseType(typeof(GpsLocationResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<GpsLocationResponse>> PostGpsLocation([FromBody] GpsLocationRequest request)
    {
        var command = new CreateGpsLocationCommand
        {
            DeviceId = request.DeviceId,
            VehiclePlate = request.VehiclePlate,
            Latitude = request.Latitude,
            Longitude = request.Longitude,
            Altitude = request.Altitude,
            Speed = request.Speed,
            Heading = request.Heading,
            Accuracy = request.Accuracy,
            Timestamp = request.Timestamp,
            BatteryLevel = request.BatteryLevel
        };

        var result = await _mediator.Send(command);
        return CreatedAtAction(nameof(PostGpsLocation), new { id = result.Id }, result);
    }

    #endregion
}

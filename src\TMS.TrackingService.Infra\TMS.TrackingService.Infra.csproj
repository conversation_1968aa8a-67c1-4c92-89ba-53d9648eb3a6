﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Data\Configurations\TodoConfiguration.cs" />
    <Compile Remove="Migrations\20250810071028_InitialCreate.cs" />
    <Compile Remove="Migrations\20250810071028_InitialCreate.Designer.cs" />
    <Compile Remove="Migrations\ApplicationDbContextModelSnapshot.cs" />
    <Compile Remove="Repositories\TodoRepository.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Data\Migrations\001_CreateTodosTable.sql" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
    <PackageReference Include="TMS.SharedKernel.EntityFrameworkCore" Version="1.*" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TMS.TrackingService.Domain\TMS.TrackingService.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Data\Configurations\" />
    <Folder Include="Data\Migrations\" />
    <Folder Include="Migrations\" />
    <Folder Include="Repositories\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="DatabaseInitializer\SchemasDefault.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DatabaseInitializer\SchemasQuartz.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>

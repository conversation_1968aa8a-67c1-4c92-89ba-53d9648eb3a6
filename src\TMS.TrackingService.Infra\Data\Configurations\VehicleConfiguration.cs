using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data.Configurations;

public class VehicleConfiguration : IEntityTypeConfiguration<Vehicle>
{
    public void Configure(EntityTypeBuilder<Vehicle> builder)
    {
        builder.ToTable("Vehicles");

        builder.HasKey(v => v.Id);

        builder.Property(v => v.VietmapId)
            .IsRequired();

        builder.Property(v => v.Plate)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(v => v.ActualPlate)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(v => v.GroupName)
            .HasMaxLength(200);

        builder.Property(v => v.VehicleTypeName)
            .HasMaxLength(100);

        builder.Property(v => v.Vin)
            .HasMaxLength(50);

        builder.Property(v => v.BrandName)
            .HasMaxLength(100);

        builder.Property(v => v.ProductYear)
            .HasMaxLength(10);

        // Indexes
        builder.HasIndex(v => v.VietmapId)
            .IsUnique();

        builder.HasIndex(v => v.Plate);

        builder.HasIndex(v => v.GroupId);

        // Relationships
        builder.HasMany(v => v.VehicleStatuses)
            .WithOne(vs => vs.Vehicle)
            .HasForeignKey(vs => vs.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(v => v.DailyReports)
            .WithOne(dr => dr.Vehicle)
            .HasForeignKey(dr => dr.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(v => v.VehicleTrips)
            .WithOne(vt => vt.Vehicle)
            .HasForeignKey(vt => vt.VehicleId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

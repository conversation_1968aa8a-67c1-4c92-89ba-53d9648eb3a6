using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Tollgate in/out report entity
/// </summary>
public class TollgateReport : AuditableEntity<Guid>
{
    public Guid VehicleId { get; set; }
    public Guid? DriverId { get; set; }
    public Guid? FromTollgateId { get; set; }
    public Guid? ToTollgateId { get; set; }
    public DateTime FromTime { get; set; }
    public DateTime ToTime { get; set; }
    public int Duration { get; set; }
    public string FromTollgateName { get; set; } = string.Empty;
    public string ToTollgateName { get; set; } = string.Empty;
    public decimal TollgatePrice { get; set; }
    public string? FromAddress { get; set; }
    public string? ToAddress { get; set; }

    // Navigation properties
    public Vehicle Vehicle { get; set; } = null!;
    public Driver? Driver { get; set; }
    public Tollgate? FromTollgate { get; set; }
    public Tollgate? ToTollgate { get; set; }
}

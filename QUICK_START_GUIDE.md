# Quick Start Guide - Vietmap Tracking Service

## Prerequisites
- .NET 9.0 SDK
- SQL Server 2019+
- Vietmap API Key

## Step 1: Configuration

### 1.1 Update appsettings.json

```json
{
  "Vietmap": {
    "ApiKey": "YOUR_VIETMAP_API_KEY_HERE",
    "PullingCron": "0 */5 * * * ?"
  },
  "ConnectionStrings": {
    "TrackingDBConnection": "Server=localhost;Database=TMS_Tracking;User Id=sa;Password=YourPassword;TrustServerCertificate=True;"
  }
}
```

### 1.2 Get Your Vietmap API Key
Contact Vietmap support to obtain your API key.

## Step 2: Database Setup

### 2.1 Create Migration

```bash
cd src/TMS.TrackingService.Api
dotnet ef migrations add InitialVietmapSchema --project ../TMS.TrackingService.Infra
```

### 2.2 Update Database

```bash
dotnet ef database update --project ../TMS.TrackingService.Infra
```

Or the application will auto-create on first run (see Program.cs:65)

## Step 3: Build and Run

```bash
dotnet build
dotnet run --project src/TMS.TrackingService.Api
```

## Step 4: Verify

### 4.1 Check Swagger UI
Open browser: `https://localhost:5001/swagger`

### 4.2 Test API Endpoints

#### Get Vehicles
```http
GET /api/v1/tracking/vehicles?apikey=YOUR_API_KEY
```

#### Get Vehicle Status
```http
GET /api/v1/tracking/vehicles/29LD-000.00/status?apikey=YOUR_API_KEY
```

### 4.3 Monitor Job Execution

Check application logs for job execution:

```
Starting Vietmap Pulling Job - Instance: abc123
--- Step 1: Syncing Master Data ---
✓ Synced 50 vehicles
✓ Synced 120 drivers
...
```

## Step 5: Database Verification

```sql
-- Check synced vehicles
SELECT COUNT(*) FROM Vehicles;
SELECT TOP 10 * FROM Vehicles;

-- Check vehicle statuses
SELECT COUNT(*) FROM VehicleStatuses;
SELECT TOP 10 * FROM VehicleStatuses ORDER BY GpsTime DESC;

-- Check drivers
SELECT COUNT(*) FROM Drivers;
SELECT TOP 10 * FROM Drivers;

-- Check daily reports
SELECT COUNT(*) FROM DailyReports;
SELECT TOP 10 * FROM DailyReports ORDER BY Date DESC;
```

## Troubleshooting

### Issue: Job Not Running

**Check Quartz Logs**:
```json
{
  "Logging": {
    "LogLevel": {
      "Quartz": "Debug"
    }
  }
}
```

**Verify Job Schedule**:
```sql
SELECT * FROM QRTZ_TRIGGERS WHERE TRIGGER_NAME = 'VietmapPullingJob-trigger';
```

### Issue: No Data Synced

**Check API Key**:
- Verify `Vietmap:ApiKey` in appsettings.json
- Test API key with Postman/curl

**Check Logs**:
```bash
# Look for errors in logs
grep "Error" logs/app.log
```

### Issue: Database Connection Failed

**Test Connection String**:
```bash
dotnet ef database update --project src/TMS.TrackingService.Infra --connection "YOUR_CONNECTION_STRING"
```

## Cron Schedule Examples

⚠️ **Important**: Quartz.NET cron uses `?` for day-of-month when specifying day-of-week

| Description | Cron Expression | Frequency |
|-------------|----------------|-----------|
| Every 1 minute | `0 */1 * ? * *` | 60 times/hour |
| Every 5 minutes | `0 */5 * ? * *` | 12 times/hour |
| Every 10 minutes | `0 */10 * ? * *` | 6 times/hour |
| Every 30 minutes | `0 */30 * ? * *` | 2 times/hour |
| Every hour | `0 0 * ? * *` | 24 times/day |
| Every 3 hours | `0 0 */3 ? * *` | 8 times/day |
| Daily at midnight | `0 0 0 ? * *` | Once/day |
| Daily at 2 AM | `0 0 2 ? * *` | Once/day |

## Testing Individual Sync Operations

You can create a test controller or console app to test individual sync operations:

```csharp
using var scope = app.Services.CreateScope();
var syncService = scope.ServiceProvider.GetRequiredService<IVietmapSyncService>();

// Test vehicle sync
var vehicleCount = await syncService.SyncVehiclesAsync();
Console.WriteLine($"Synced {vehicleCount} vehicles");

// Test driver sync
var driverCount = await syncService.SyncDriversAsync();
Console.WriteLine($"Synced {driverCount} drivers");

// Test vehicle status sync
var statusCount = await syncService.SyncVehicleStatusesAsync();
Console.WriteLine($"Synced {statusCount} statuses");
```

## Performance Tuning

### For Small Fleets (<50 vehicles)
```json
{
  "Vietmap": {
    "PullingCron": "0 */5 * ? * *"
  }
}
```

### For Medium Fleets (50-200 vehicles)
```json
{
  "Vietmap": {
    "PullingCron": "0 */10 * ? * *"
  }
}
```

### For Large Fleets (>200 vehicles)
```json
{
  "Vietmap": {
    "PullingCron": "0 */15 * ? * *"
  }
}
```

## Next Steps

1. **Set up monitoring**: Add Application Insights or Prometheus
2. **Configure alerts**: Set up alerts for job failures
3. **Optimize schedule**: Adjust cron based on your needs
4. **Add dashboards**: Create dashboards for vehicle tracking
5. **Implement caching**: Add Redis for frequently accessed data

## API Endpoints Summary

All endpoints are documented in Swagger UI at `/swagger`

### Master Data
- `GET /api/v1/tracking/vehicles` - Get all vehicles
- `GET /api/v1/tracking/drivers` - Get all drivers
- `GET /api/v1/tracking/tollgates` - Get all tollgates

### Real-time
- `GET /api/v1/tracking/vehicles/{plate}/status` - Get vehicle status

### Historical
- `GET /api/v1/tracking/vehicles/{id}/history` - Get vehicle history
- `GET /api/v1/tracking/vehicles/{id}/images` - Get vehicle images

### Reports
- `GET /api/v1/tracking/reports/daily` - Daily reports
- `GET /api/v1/tracking/reports/shifts` - Driver shifts
- `GET /api/v1/tracking/reports/trips` - Vehicle trips
- `GET /api/v1/tracking/reports/tollgates` - Tollgate reports
- `GET /api/v1/tracking/reports/violations` - Telematics violations
- `GET /api/v1/tracking/reports/fuel` - Fuel reports
- `GET /api/v1/tracking/reports/temperature` - Temperature reports
- `GET /api/v1/tracking/reports/maintenance` - Maintenance reports

### Maintenance
- `GET /api/v1/tracking/maintenance` - Maintenance management

## Support

For issues or questions:
1. Check the logs first
2. Review the documentation
3. Contact your system administrator

## Resources

- **API Documentation**: `/swagger`
- **Implementation Guide**: `VIETMAP_API_IMPLEMENTATION.md`
- **Job Guide**: `VIETMAP_JOB_IMPLEMENTATION.md`
- **Database Schema**: `DATABASE_SCHEMA.sql`

using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Telematics violation entity (overspeed, harsh braking, etc.)
/// </summary>
public class TelematicsViolation : AuditableEntity<Guid>
{
    public Guid VehicleId { get; set; }
    public Guid DriverId { get; set; }
    public DateTime ViolationTime { get; set; }

    /// <summary>
    /// HARSH_ACCELERATION, HARSH_BREAK, OVERSPEED, OVERDRIVING4H, OVERDRIVING10H
    /// </summary>
    public string BehaviorType { get; set; } = string.Empty;

    public string? Address { get; set; }
    public double? Longitude { get; set; }
    public double? Latitude { get; set; }

    /// <summary>
    /// Behavior description stored as JSON
    /// </summary>
    public string? BehaviorDescriptionJson { get; set; }

    // Navigation properties
    public Vehicle Vehicle { get; set; } = null!;
    public Driver Driver { get; set; } = null!;
}

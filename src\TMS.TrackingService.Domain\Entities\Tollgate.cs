using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Tollgate entity
/// </summary>
public class Tollgate : AuditableEntity<Guid>
{
    public string VietmapId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Address { get; set; }
    public double X { get; set; }
    public double Y { get; set; }
    public int Heading { get; set; }

    // Navigation properties
    public ICollection<TollgateReport> TollgateReports { get; set; } = new List<TollgateReport>();
}

using Microsoft.Extensions.Configuration;

namespace TMS.TrackingService.Api;

/// <summary>
/// DelegatingHandler to add Vietmap API key to all requests
/// </summary>
public class VietmapAuthenticationHandler : DelegatingHandler
{
    private readonly string _apiKey;

    public VietmapAuthenticationHandler(IConfiguration configuration)
    {
        _apiKey = configuration["Vietmap:ApiKey"] ?? throw new InvalidOperationException("Vietmap API Key is not configured");
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // Add apikey query parameter to all requests
        var uriBuilder = new UriBuilder(request.RequestUri!);
        var query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
        query["apikey"] = _apiKey;
        uriBuilder.Query = query.ToString();
        request.RequestUri = uriBuilder.Uri;

        return await base.SendAsync(request, cancellationToken);
    }
}

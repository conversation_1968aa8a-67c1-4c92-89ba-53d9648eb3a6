﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Features\Todos\**" />
    <EmbeddedResource Remove="Features\Todos\**" />
    <None Remove="Features\Todos\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.9.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.0" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
    <PackageReference Include="Quartz" Version="3.15.0" />
<PackageReference Include="TMS.SharedKernel.Constants" Version="1.*" />
    </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TMS.TrackingService.ApiClient\TMS.TrackingService.ApiClient.csproj" />
    <ProjectReference Include="..\TMS.TrackingService.Domain\TMS.TrackingService.Domain.csproj" />
    <ProjectReference Include="..\TMS.TrackingService.Contracts\TMS.TrackingService.Contracts.csproj" />
    <ProjectReference Include="..\TMS.TrackingService.Infra\TMS.TrackingService.Infra.csproj" />
  </ItemGroup>

</Project>

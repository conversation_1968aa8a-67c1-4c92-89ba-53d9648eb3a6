using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.TrackingService.Contracts.Vietmap;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Features.Tracking.Queries.GetDrivers;

public class GetDriversQueryHandler : IRequestHandler<GetDriversQuery, List<DriverDto>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<GetDriversQueryHandler> _logger;

    public GetDriversQueryHandler(ApplicationDbContext dbContext, IMapper mapper, ILogger<GetDriversQueryHandler> logger)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<DriverDto>> Handle(GetDriversQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var drivers = await _dbContext.Drivers
                .AsNoTracking()
                .OrderBy(d => d.Name)
                .ToListAsync(cancellationToken);

            return _mapper.Map<List<DriverDto>>(drivers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching drivers from database");
            return new List<DriverDto>();
        }
    }
}

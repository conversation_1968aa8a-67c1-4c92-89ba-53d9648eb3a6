using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data.Configurations;

public class GpsLocationConfiguration : IEntityTypeConfiguration<GpsLocation>
{
    public void Configure(EntityTypeBuilder<GpsLocation> builder)
    {
        builder.ToTable("gps_locations");

        builder.HasKey(g => g.Id);

        builder.Property(g => g.Id)
            .HasColumnName("id")
            .HasDefaultValueSql("gen_random_uuid()");

        builder.Property(g => g.DeviceId)
            .HasColumnName("device_id")
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(g => g.VehiclePlate)
            .HasColumnName("vehicle_plate")
            .HasMaxLength(50);

        builder.Property(g => g.Latitude)
            .HasColumnName("latitude")
            .HasColumnType("double precision")
            .IsRequired();

        builder.Property(g => g.Longitude)
            .HasColumnName("longitude")
            .HasColumnType("double precision")
            .IsRequired();

        builder.Property(g => g.Altitude)
            .HasColumnName("altitude")
            .HasColumnType("double precision");

        builder.Property(g => g.Speed)
            .HasColumnName("speed")
            .HasColumnType("double precision");

        builder.Property(g => g.Heading)
            .HasColumnName("heading")
            .HasColumnType("double precision");

        builder.Property(g => g.Accuracy)
            .HasColumnName("accuracy")
            .HasColumnType("double precision");

        builder.Property(g => g.Timestamp)
            .HasColumnName("timestamp")
            .HasColumnType("timestamp with time zone")
            .IsRequired();

        builder.Property(g => g.BatteryLevel)
            .HasColumnName("battery_level");

        builder.Property(g => g.CreatedAt)
            .HasColumnName("created_at")
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(g => g.UpdatedAt)
            .HasColumnName("updated_at")
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(g => g.UpdatedBy)
            .HasColumnName("updated_by")
            .HasColumnType("UUID");

        builder.Property(g => g.CreatedBy)
            .HasColumnName("created_by")
            .HasColumnType("UUID");

        // Indexes for performance optimization (1-second intervals)
        builder.HasIndex(g => g.DeviceId)
            .HasDatabaseName("idx_gps_locations_device_id");

        builder.HasIndex(g => g.VehiclePlate)
            .HasDatabaseName("idx_gps_locations_vehicle_plate");

        builder.HasIndex(g => g.Timestamp)
            .HasDatabaseName("idx_gps_locations_timestamp");

        builder.HasIndex(g => g.CreatedAt)
            .HasDatabaseName("idx_gps_locations_created_at");

        // Composite index for common queries (device + timestamp)
        builder.HasIndex(g => new { g.DeviceId, g.Timestamp })
            .HasDatabaseName("idx_gps_locations_device_timestamp");

        // Composite index for vehicle tracking queries
        builder.HasIndex(g => new { g.VehiclePlate, g.Timestamp })
            .HasDatabaseName("idx_gps_locations_vehicle_timestamp");
    }
}

using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.TrackingService.Contracts.Vietmap;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Features.Tracking.Queries.GetVehicleTripReports;

public class GetVehicleTripReportsQueryHandler : IRequestHandler<GetVehicleTripReportsQuery, List<VehicleTripReportDto>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleTripReportsQueryHandler> _logger;

    public GetVehicleTripReportsQueryHandler(ApplicationDbContext dbContext, IMapper mapper, ILogger<GetVehicleTripReportsQueryHandler> logger)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<VehicleTripReportDto>> Handle(GetVehicleTripReportsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Parse date range
            if (!DateTime.TryParse(request.From, out var fromDate) || !DateTime.TryParse(request.To, out var toDate))
            {
                _logger.LogWarning("Invalid date format: From={From}, To={To}", request.From, request.To);
                return new List<VehicleTripReportDto>();
            }

            // Parse vehicle IDs (comma-separated Vietmap IDs)
            var vehicleIds = request.Vehicles.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(v => int.TryParse(v.Trim(), out var id) ? id : 0)
                .Where(id => id > 0)
                .ToList();

            if (!vehicleIds.Any())
            {
                _logger.LogWarning("Invalid vehicle IDs: {Vehicles}", request.Vehicles);
                return new List<VehicleTripReportDto>();
            }

            // Get vehicles by Vietmap IDs
            var vehicles = await _dbContext.Vehicles
                .AsNoTracking()
                .Where(v => vehicleIds.Contains(v.VietmapId))
                .ToListAsync(cancellationToken);

            if (!vehicles.Any())
            {
                _logger.LogWarning("No vehicles found for IDs: {VehicleIds}", string.Join(", ", vehicleIds));
                return new List<VehicleTripReportDto>();
            }

            var vehicleGuids = vehicles.Select(v => v.Id).ToList();

            // Get trip reports for the date range
            var trips = await _dbContext.VehicleTrips
                .AsNoTracking()
                .Where(vt => vehicleGuids.Contains(vt.VehicleId) && vt.FromTime >= fromDate && vt.ToTime <= toDate)
                .OrderBy(vt => vt.FromTime)
                .ToListAsync(cancellationToken);

            return _mapper.Map<List<VehicleTripReportDto>>(trips);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching vehicle trip reports from database");
            return new List<VehicleTripReportDto>();
        }
    }
}

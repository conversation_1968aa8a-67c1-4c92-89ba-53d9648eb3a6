using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Infra.Data.Configurations;

public class DriverShiftConfiguration : IEntityTypeConfiguration<DriverShift>
{
    public void Configure(EntityTypeBuilder<DriverShift> builder)
    {
        builder.ToTable("DriverShifts");

        builder.HasKey(ds => ds.Id);

        builder.Property(ds => ds.VehicleId)
            .IsRequired();

        builder.Property(ds => ds.DriverId)
            .IsRequired();

        builder.Property(ds => ds.Plate)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(ds => ds.DriverName)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(ds => ds.LicenseNo)
            .HasMaxLength(50);

        builder.Property(ds => ds.IdNo)
            .HasMaxLength(50);

        builder.Property(ds => ds.FromAddress)
            .HasMaxLength(500);

        builder.Property(ds => ds.ToAddress)
            .HasMaxLength(500);

        // Indexes
        builder.HasIndex(ds => ds.VehicleId);

        builder.HasIndex(ds => ds.DriverId);

        builder.HasIndex(ds => ds.FromTime);

        builder.HasIndex(ds => new { ds.VehicleId, ds.DriverId, ds.FromTime });

        // Relationships
        builder.HasOne(ds => ds.Vehicle)
            .WithMany()
            .HasForeignKey(ds => ds.VehicleId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(ds => ds.Driver)
            .WithMany(d => d.DriverShifts)
            .HasForeignKey(ds => ds.DriverId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}

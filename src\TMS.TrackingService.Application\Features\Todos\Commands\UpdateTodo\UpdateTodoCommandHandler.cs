﻿using MapsterMapper;
using MediatR;
using TMS.SharedKernel.Domain;
using TMS.TrackingService.Contracts.Todos;
using TMS.TrackingService.Domain.Entities;

namespace TMS.TrackingService.Application.Features.Todos.Commands.UpdateTodo;

public class UpdateTodoCommandHandler : IRequestHandler<UpdateTodoCommand, TodoResponse>
{
    private readonly IBaseRepository<Todo> _todoRepository;
    private readonly IMapper _mapper;

    public UpdateTodoCommandHandler(IBaseRepository<Todo> todoRepository, IMapper mapper)
    {
        _todoRepository = todoRepository;
        _mapper = mapper;
    }

    public async Task<TodoResponse> Handle(UpdateTodoCommand request, CancellationToken cancellationToken)
    {
        var existingTodo = await _todoRepository.GetByIdAsync(request.Id, cancellationToken);

        if (existingTodo == null)
            throw new KeyNotFoundException($"Todo with ID {request.Id} not found");

        bool wasCompleted = existingTodo.IsCompleted;

        existingTodo.Title = request.Title;
        existingTodo.Description = request.Description;
        existingTodo.IsCompleted = request.IsCompleted;

        if (request.IsCompleted && !wasCompleted)
            existingTodo.CompletedAt = DateTime.UtcNow;
        else if (!request.IsCompleted)
            existingTodo.CompletedAt = null;

        _todoRepository.Update(existingTodo);

        // Return the mapped TodoResponse
        return _mapper.Map<TodoResponse>(existingTodo);
    }
}


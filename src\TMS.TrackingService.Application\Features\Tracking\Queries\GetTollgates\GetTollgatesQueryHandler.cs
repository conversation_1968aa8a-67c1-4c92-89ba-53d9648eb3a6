using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.TrackingService.Contracts.Vietmap;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Features.Tracking.Queries.GetTollgates;

public class GetTollgatesQueryHandler : IRequestHandler<GetTollgatesQuery, List<TollgateDto>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<GetTollgatesQueryHandler> _logger;

    public GetTollgatesQueryHandler(ApplicationDbContext dbContext, IMapper mapper, ILogger<GetTollgatesQueryHandler> logger)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<TollgateDto>> Handle(GetTollgatesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var tollgates = await _dbContext.Tollgates
                .AsNoTracking()
                .OrderBy(t => t.Name)
                .ToListAsync(cancellationToken);

            return _mapper.Map<List<TollgateDto>>(tollgates);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching tollgates from database");
            return new List<TollgateDto>();
        }
    }
}

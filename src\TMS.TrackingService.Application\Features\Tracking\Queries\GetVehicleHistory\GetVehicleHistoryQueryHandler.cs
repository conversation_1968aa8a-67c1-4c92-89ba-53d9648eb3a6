using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.TrackingService.Contracts.Vietmap;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Features.Tracking.Queries.GetVehicleHistory;

public class GetVehicleHistoryQueryHandler : IRequestHandler<GetVehicleHistoryQuery, List<VehicleHistoryDto>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehicleHistoryQueryHandler> _logger;

    public GetVehicleHistoryQueryHandler(ApplicationDbContext dbContext, IMapper mapper, ILogger<GetVehicleHistoryQueryHandler> logger)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<VehicleHistoryDto>> Handle(GetVehicleHistoryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Parse date range
            if (!DateTime.TryParse(request.From, out var fromDate) || !DateTime.TryParse(request.To, out var toDate))
            {
                _logger.LogWarning("Invalid date format: From={From}, To={To}", request.From, request.To);
                return new List<VehicleHistoryDto>();
            }

            // Get vehicle by Vietmap ID
            var vehicle = await _dbContext.Vehicles
                .AsNoTracking()
                .FirstOrDefaultAsync(v => v.VietmapId == request.VietmapVehicleId, cancellationToken);

            if (vehicle == null)
            {
                _logger.LogWarning("Vehicle with Vietmap ID {VietmapId} not found", request.VietmapVehicleId);
                return new List<VehicleHistoryDto>();
            }

            // Get vehicle statuses (history) for the date range
            var statuses = await _dbContext.VehicleStatuses
                .AsNoTracking()
                .Where(vs => vs.VehicleId == vehicle.Id && vs.GpsTime >= fromDate && vs.GpsTime <= toDate)
                .OrderBy(vs => vs.GpsTime)
                .ToListAsync(cancellationToken);

            return _mapper.Map<List<VehicleHistoryDto>>(statuses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching vehicle history from database");
            return new List<VehicleHistoryDto>();
        }
    }
}

﻿using Microsoft.Extensions.Logging;
using Quartz;
using TMS.TrackingService.Application.Services;

namespace TMS.TrackingService.Application.Job;

/// <summary>
/// Quartz job for pulling data from Vietmap API and persisting to database
/// Executes sequentially to avoid concurrent API calls and database conflicts
/// </summary>
[DisallowConcurrentExecution]
public class VietmapPullingJob : IJob
{
    private readonly IVietmapSyncService _syncService;
    private readonly ILogger<VietmapPullingJob> _logger;

    public VietmapPullingJob(
        IVietmapSyncService syncService,
        ILogger<VietmapPullingJob> logger)
    {
        _syncService = syncService;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var cancellationToken = context.CancellationToken;
        var jobId = context.FireInstanceId;
        var startTime = DateTime.UtcNow;

        _logger.LogInformation("========================================");
        _logger.LogInformation("Starting Vietmap Pulling Job - Instance: {JobId}", jobId);
        _logger.LogInformation("========================================");

        try
        {
            // Step 1: Sync Master Data (less frequent updates)
            await SyncMasterDataAsync(cancellationToken);

            // Step 2: Sync Real-time Data
            await SyncRealTimeDataAsync(cancellationToken);

            // Step 3: Sync Historical Data (last 24 hours)
            await SyncHistoricalDataAsync(cancellationToken);

            // Step 4: Sync Reports (yesterday's data)
            await SyncReportsAsync(cancellationToken);

            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("========================================");
            _logger.LogInformation("Vietmap Pulling Job completed successfully in {Duration:mm\\:ss}", duration);
            _logger.LogInformation("========================================");
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Vietmap Pulling Job was cancelled - Instance: {JobId}", jobId);
            throw;
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Vietmap Pulling Job failed after {Duration:mm\\:ss} - Instance: {JobId}", duration, jobId);

            // Store error in job context for monitoring
            context.Result = new JobExecutionResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                ExecutionTime = duration
            };

            throw new JobExecutionException(ex, refireImmediately: false);
        }
    }

    /// <summary>
    /// Step 1: Sync master data (vehicles, drivers, tollgates)
    /// These are relatively static and can be synced less frequently
    /// </summary>
    private async Task SyncMasterDataAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("--- Step 1: Syncing Master Data ---");

        try
        {
            // 1.1 Sync Vehicles
            _logger.LogInformation("1.1 Syncing vehicles...");
            var vehicleCount = await _syncService.SyncVehiclesAsync(cancellationToken);
            _logger.LogInformation("✓ Synced {Count} vehicles", vehicleCount);

            // 1.2 Sync Drivers
            _logger.LogInformation("1.2 Syncing drivers...");
            var driverCount = await _syncService.SyncDriversAsync(cancellationToken);
            _logger.LogInformation("✓ Synced {Count} drivers", driverCount);

            // 1.3 Sync Tollgates
            _logger.LogInformation("1.3 Syncing tollgates...");
            var tollgateCount = await _syncService.SyncTollgatesAsync(cancellationToken);
            _logger.LogInformation("✓ Synced {Count} tollgates", tollgateCount);

            _logger.LogInformation("--- Master Data Sync Completed ---");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing master data");
            throw;
        }
    }

    /// <summary>
    /// Step 2: Sync real-time data (current vehicle statuses)
    /// This provides the latest GPS positions
    /// </summary>
    private async Task SyncRealTimeDataAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("--- Step 2: Syncing Real-time Data ---");

        try
        {
            // 2.1 Sync Vehicle Statuses (current positions)
            _logger.LogInformation("2.1 Syncing vehicle statuses...");
            var statusCount = await _syncService.SyncVehicleStatusesAsync(cancellationToken);
            _logger.LogInformation("✓ Synced {Count} vehicle statuses", statusCount);

            _logger.LogInformation("--- Real-time Data Sync Completed ---");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing real-time data");
            throw;
        }
    }

    /// <summary>
    /// Step 3: Sync historical data (last 24 hours)
    /// This includes trips, shifts, and tollgate passages
    /// </summary>
    private async Task SyncHistoricalDataAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("--- Step 3: Syncing Historical Data (Last 24 Hours) ---");

        try
        {
            var toDate = DateTime.Now;
            var fromDate = toDate.AddHours(-24);

            // 3.1 Sync Driver Shifts
            _logger.LogInformation("3.1 Syncing driver shifts from {From} to {To}...", fromDate, toDate);
            var shiftCount = await _syncService.SyncDriverShiftsAsync(fromDate, toDate, cancellationToken);
            _logger.LogInformation("✓ Synced {Count} driver shifts", shiftCount);

            // 3.2 Sync Vehicle Trips
            _logger.LogInformation("3.2 Syncing vehicle trips from {From} to {To}...", fromDate, toDate);
            var tripCount = await _syncService.SyncVehicleTripsAsync(fromDate, toDate, cancellationToken);
            _logger.LogInformation("✓ Synced {Count} vehicle trips", tripCount);

            // 3.3 Sync Tollgate Reports
            _logger.LogInformation("3.3 Syncing tollgate reports from {From} to {To}...", fromDate, toDate);
            var tollgateReportCount = await _syncService.SyncTollgateReportsAsync(fromDate, toDate, cancellationToken);
            _logger.LogInformation("✓ Synced {Count} tollgate reports", tollgateReportCount);

            // 3.4 Sync Telematics Violations
            _logger.LogInformation("3.4 Syncing telematics violations from {From} to {To}...", fromDate, toDate);
            var violationCount = await _syncService.SyncTelematicsViolationsAsync(fromDate, toDate, cancellationToken);
            _logger.LogInformation("✓ Synced {Count} telematics violations", violationCount);

            _logger.LogInformation("--- Historical Data Sync Completed ---");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing historical data");
            throw;
        }
    }

    /// <summary>
    /// Step 4: Sync reports (yesterday's aggregated data)
    /// This includes daily summaries and maintenance records
    /// </summary>
    private async Task SyncReportsAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("--- Step 4: Syncing Reports (Yesterday) ---");

        try
        {
            var yesterday = DateTime.Now.Date.AddDays(-1);
            var fromDate = yesterday;
            var toDate = yesterday.AddDays(1).AddSeconds(-1);

            // 4.1 Sync Daily Reports
            _logger.LogInformation("4.1 Syncing daily reports for {Date}...", yesterday);
            var dailyReportCount = await _syncService.SyncDailyReportsAsync(fromDate, toDate, cancellationToken);
            _logger.LogInformation("✓ Synced {Count} daily reports", dailyReportCount);

            // 4.2 Sync Maintenance Records
            _logger.LogInformation("4.2 Syncing maintenance records...");
            var maintenanceCount = await _syncService.SyncMaintenanceRecordsAsync(cancellationToken);
            _logger.LogInformation("✓ Synced {Count} maintenance records", maintenanceCount);

            _logger.LogInformation("--- Reports Sync Completed ---");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing reports");
            throw;
        }
    }
}

/// <summary>
/// Result object for job execution tracking
/// </summary>
public class JobExecutionResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public Dictionary<string, int> SyncCounts { get; set; } = new();
}

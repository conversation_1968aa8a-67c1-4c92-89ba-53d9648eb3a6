-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Vietmap Tracking Service Database Schema (PostgreSQL)
-- This script provides the complete database schema for all tracking entities

-- =============================================
-- DROP EXISTING PARTITIONED TABLES (IF EXISTS)
-- =============================================
DROP TABLE IF EXISTS "VehicleStatuses" CASCADE;
DROP TABLE IF EXISTS "gps_locations" CASCADE;
DROP TABLE IF EXISTS "VehicleTrips" CASCADE;
DROP TABLE IF EXISTS "DriverShifts" CASCADE;
DROP TABLE IF EXISTS "TelematicsViolations" CASCADE;

-- =============================================
-- 1. Vehicles Table
-- =============================================
CREATE TABLE IF NOT EXISTS "Vehicles" (
    "Id" UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "VietmapId" INTEGER NOT NULL,
    "Plate" VARCHAR(20) NOT NULL,
    "ActualPlate" VARCHAR(20) NOT NULL,
    "GroupId" INTEGER NOT NULL,
    "GroupName" VARCHAR(200) NOT NULL,
    "VehicleTypeId" INTEGER NOT NULL,
    "VehicleTypeName" VARCHAR(100) NOT NULL,
    "Capacity" DOUBLE PRECISION NOT NULL,
    "Vin" VARCHAR(50),
    "BrandName" VARCHAR(100),
    "ProductYear" VARCHAR(10),
    "RegisterDate" TIMESTAMP WITH TIME ZONE,
    "MaxSpeed" DOUBLE PRECISION,
    "FuelPer100km" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "FuelPerIdleHour" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "FuelPerIgnitionHour" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "EmissionsHour" INTEGER NOT NULL DEFAULT 0,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    CONSTRAINT "UK_Vehicles_VietmapId" UNIQUE ("VietmapId")
);

CREATE INDEX IF NOT EXISTS "IX_Vehicles_Plate" ON "Vehicles" ("Plate");
CREATE INDEX IF NOT EXISTS "IX_Vehicles_GroupId" ON "Vehicles" ("GroupId");

-- =============================================
-- 2. VehicleStatuses Table (PARTITIONED BY GpsTime - Monthly)
-- =============================================
CREATE TABLE "VehicleStatuses" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "VehicleId" UUID NOT NULL,
    "GpsTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "SysTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "X" DOUBLE PRECISION NOT NULL,
    "Y" DOUBLE PRECISION NOT NULL,
    "Status" INTEGER NOT NULL DEFAULT 0,
    "Speed" INTEGER NOT NULL DEFAULT 0,
    "Heading" INTEGER NOT NULL DEFAULT 0,
    "EventId" INTEGER,
    "Distance" BIGINT NOT NULL DEFAULT 0,
    "DriverName" VARCHAR(200),
    "LicenseNo" VARCHAR(50),
    "Address" VARCHAR(500),
    "SensorsJson" TEXT,
    "ContainerId" VARCHAR(50),
    "ContainerName" VARCHAR(200),
    "ContainerSerial" VARCHAR(100),
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    PRIMARY KEY ("Id", "GpsTime"),
    CONSTRAINT "FK_VehicleStatuses_Vehicles" FOREIGN KEY ("VehicleId")
        REFERENCES "Vehicles"("Id") ON DELETE CASCADE
) PARTITION BY RANGE ("GpsTime");

-- Create monthly partitions for VehicleStatuses (current month + next 12 months)
CREATE TABLE "VehicleStatuses_2025_01" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
CREATE TABLE "VehicleStatuses_2025_02" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
CREATE TABLE "VehicleStatuses_2025_03" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');
CREATE TABLE "VehicleStatuses_2025_04" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-04-01') TO ('2025-05-01');
CREATE TABLE "VehicleStatuses_2025_05" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-05-01') TO ('2025-06-01');
CREATE TABLE "VehicleStatuses_2025_06" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');
CREATE TABLE "VehicleStatuses_2025_07" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');
CREATE TABLE "VehicleStatuses_2025_08" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');
CREATE TABLE "VehicleStatuses_2025_09" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');
CREATE TABLE "VehicleStatuses_2025_10" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');
CREATE TABLE "VehicleStatuses_2025_11" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');
CREATE TABLE "VehicleStatuses_2025_12" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');
CREATE TABLE "VehicleStatuses_2026_01" PARTITION OF "VehicleStatuses"
    FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

-- Create indexes on partitions
CREATE INDEX "IX_VehicleStatuses_2025_01_VehicleId_GpsTime" ON "VehicleStatuses_2025_01" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_02_VehicleId_GpsTime" ON "VehicleStatuses_2025_02" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_03_VehicleId_GpsTime" ON "VehicleStatuses_2025_03" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_04_VehicleId_GpsTime" ON "VehicleStatuses_2025_04" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_05_VehicleId_GpsTime" ON "VehicleStatuses_2025_05" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_06_VehicleId_GpsTime" ON "VehicleStatuses_2025_06" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_07_VehicleId_GpsTime" ON "VehicleStatuses_2025_07" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_08_VehicleId_GpsTime" ON "VehicleStatuses_2025_08" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_09_VehicleId_GpsTime" ON "VehicleStatuses_2025_09" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_10_VehicleId_GpsTime" ON "VehicleStatuses_2025_10" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_11_VehicleId_GpsTime" ON "VehicleStatuses_2025_11" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2025_12_VehicleId_GpsTime" ON "VehicleStatuses_2025_12" ("VehicleId", "GpsTime" DESC);
CREATE INDEX "IX_VehicleStatuses_2026_01_VehicleId_GpsTime" ON "VehicleStatuses_2026_01" ("VehicleId", "GpsTime" DESC);

-- =============================================
-- 3. DailyReports Table
-- =============================================
CREATE TABLE IF NOT EXISTS "DailyReports" (
    "Id" UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "VehicleId" UUID NOT NULL,
    "Date" TIMESTAMP WITH TIME ZONE NOT NULL,
    "Distance" BIGINT NOT NULL DEFAULT 0,
    "DoorOpenCount" INTEGER NOT NULL DEFAULT 0,
    "OverSpeedCount" INTEGER NOT NULL DEFAULT 0,
    "MaxSpeed" INTEGER NOT NULL DEFAULT 0,
    "FirstAccOnTime" TIMESTAMP WITH TIME ZONE,
    "LastAccOffTime" TIMESTAMP WITH TIME ZONE,
    "AccTime" INTEGER NOT NULL DEFAULT 0,
    "RunTime" INTEGER NOT NULL DEFAULT 0,
    "IdleTime" INTEGER NOT NULL DEFAULT 0,
    "StopTime" INTEGER NOT NULL DEFAULT 0,
    "SysTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    CONSTRAINT "FK_DailyReports_Vehicles" FOREIGN KEY ("VehicleId")
        REFERENCES "Vehicles"("Id") ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS "IX_DailyReports_VehicleId" ON "DailyReports" ("VehicleId");
CREATE INDEX IF NOT EXISTS "IX_DailyReports_Date" ON "DailyReports" ("Date");
CREATE UNIQUE INDEX IF NOT EXISTS "UK_DailyReports_VehicleId_Date" ON "DailyReports" ("VehicleId", "Date");

-- =============================================
-- 4. Drivers Table
-- =============================================
CREATE TABLE IF NOT EXISTS "Drivers" (
    "Id" UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "VietmapId" INTEGER NOT NULL,
    "Code" VARCHAR(50) NOT NULL,
    "Rfid" VARCHAR(50),
    "Name" VARCHAR(200) NOT NULL,
    "Phone" VARCHAR(20),
    "Email" VARCHAR(100),
    "Address" VARCHAR(500),
    "LicenseNo" VARCHAR(50),
    "LicenseExpireDate" TIMESTAMP WITH TIME ZONE,
    "StartDate" TIMESTAMP WITH TIME ZONE,
    "EndDate" TIMESTAMP WITH TIME ZONE,
    "Remark" VARCHAR(1000),
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    CONSTRAINT "UK_Drivers_VietmapId" UNIQUE ("VietmapId")
);

CREATE INDEX IF NOT EXISTS "IX_Drivers_Code" ON "Drivers" ("Code");
CREATE INDEX IF NOT EXISTS "IX_Drivers_LicenseNo" ON "Drivers" ("LicenseNo");

-- =============================================
-- 5. DriverShifts Table (PARTITIONED BY FromTime - Monthly)
-- =============================================
CREATE TABLE "DriverShifts" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "VehicleId" UUID NOT NULL,
    "DriverId" UUID NOT NULL,
    "Plate" VARCHAR(20) NOT NULL,
    "DriverName" VARCHAR(200) NOT NULL,
    "LicenseNo" VARCHAR(50),
    "IdNo" VARCHAR(50),
    "FromTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "ToTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "FromAddress" VARCHAR(500),
    "ToAddress" VARCHAR(500),
    "Distance" BIGINT NOT NULL DEFAULT 0,
    "DoorOpenCount" INTEGER NOT NULL DEFAULT 0,
    "DoorCloseCount" INTEGER NOT NULL DEFAULT 0,
    "OverSpeedCount" INTEGER NOT NULL DEFAULT 0,
    "MaxSpeed" INTEGER NOT NULL DEFAULT 0,
    "AccTime" INTEGER NOT NULL DEFAULT 0,
    "IdleTime" INTEGER NOT NULL DEFAULT 0,
    "RunTime" INTEGER NOT NULL DEFAULT 0,
    "StopTime" INTEGER NOT NULL DEFAULT 0,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    PRIMARY KEY ("Id", "FromTime"),
    CONSTRAINT "FK_DriverShifts_Vehicles" FOREIGN KEY ("VehicleId")
        REFERENCES "Vehicles"("Id") ON DELETE NO ACTION,
    CONSTRAINT "FK_DriverShifts_Drivers" FOREIGN KEY ("DriverId")
        REFERENCES "Drivers"("Id") ON DELETE NO ACTION
) PARTITION BY RANGE ("FromTime");

-- Create monthly partitions for DriverShifts
CREATE TABLE "DriverShifts_2025_01" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
CREATE TABLE "DriverShifts_2025_02" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
CREATE TABLE "DriverShifts_2025_03" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');
CREATE TABLE "DriverShifts_2025_04" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-04-01') TO ('2025-05-01');
CREATE TABLE "DriverShifts_2025_05" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-05-01') TO ('2025-06-01');
CREATE TABLE "DriverShifts_2025_06" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');
CREATE TABLE "DriverShifts_2025_07" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');
CREATE TABLE "DriverShifts_2025_08" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');
CREATE TABLE "DriverShifts_2025_09" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');
CREATE TABLE "DriverShifts_2025_10" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');
CREATE TABLE "DriverShifts_2025_11" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');
CREATE TABLE "DriverShifts_2025_12" PARTITION OF "DriverShifts" FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');
CREATE TABLE "DriverShifts_2026_01" PARTITION OF "DriverShifts" FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

-- Create indexes on partitions
CREATE INDEX "IX_DriverShifts_2025_01_DriverId_FromTime" ON "DriverShifts_2025_01" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_02_DriverId_FromTime" ON "DriverShifts_2025_02" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_03_DriverId_FromTime" ON "DriverShifts_2025_03" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_04_DriverId_FromTime" ON "DriverShifts_2025_04" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_05_DriverId_FromTime" ON "DriverShifts_2025_05" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_06_DriverId_FromTime" ON "DriverShifts_2025_06" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_07_DriverId_FromTime" ON "DriverShifts_2025_07" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_08_DriverId_FromTime" ON "DriverShifts_2025_08" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_09_DriverId_FromTime" ON "DriverShifts_2025_09" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_10_DriverId_FromTime" ON "DriverShifts_2025_10" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_11_DriverId_FromTime" ON "DriverShifts_2025_11" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2025_12_DriverId_FromTime" ON "DriverShifts_2025_12" ("DriverId", "FromTime" DESC);
CREATE INDEX "IX_DriverShifts_2026_01_DriverId_FromTime" ON "DriverShifts_2026_01" ("DriverId", "FromTime" DESC);

-- =============================================
-- 6. VehicleTrips Table (PARTITIONED BY FromTime - Monthly)
-- =============================================
CREATE TABLE "VehicleTrips" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "VehicleId" UUID NOT NULL,
    "DriverId" UUID,
    "FromTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "ToTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "Duration" INTEGER NOT NULL DEFAULT 0,
    "FromAddress" VARCHAR(500),
    "ToAddress" VARCHAR(500),
    "DriverName" VARCHAR(200),
    "Waypoint" INTEGER NOT NULL DEFAULT 0,
    "Status" INTEGER NOT NULL DEFAULT 0,
    "FromX" DOUBLE PRECISION NOT NULL,
    "FromY" DOUBLE PRECISION NOT NULL,
    "FromRegionId" INTEGER NOT NULL DEFAULT 0,
    "ToX" DOUBLE PRECISION NOT NULL,
    "ToY" DOUBLE PRECISION NOT NULL,
    "ToRegionId" INTEGER NOT NULL DEFAULT 0,
    "GpsDistance" BIGINT NOT NULL DEFAULT 0,
    "MaxSpeed" INTEGER NOT NULL DEFAULT 0,
    "MinSpeed" INTEGER NOT NULL DEFAULT 0,
    "AverageSpeed" INTEGER NOT NULL DEFAULT 0,
    "OverSpeed" INTEGER NOT NULL DEFAULT 0,
    "GpsMileage" BIGINT NOT NULL DEFAULT 0,
    "FromMileage" BIGINT NOT NULL DEFAULT 0,
    "ToMileage" BIGINT NOT NULL DEFAULT 0,
    "DoorOpenCount" INTEGER NOT NULL DEFAULT 0,
    "DoorCloseCount" INTEGER NOT NULL DEFAULT 0,
    "StartAtStation" BOOLEAN NOT NULL DEFAULT FALSE,
    "EndAtStation" BOOLEAN NOT NULL DEFAULT FALSE,
    "StopTime" BIGINT NOT NULL DEFAULT 0,
    "FromDistance" BIGINT NOT NULL DEFAULT 0,
    "ToDistance" BIGINT NOT NULL DEFAULT 0,
    "RestTime" INTEGER NOT NULL DEFAULT 0,
    "LicenseNo" VARCHAR(50),
    "RunTime" INTEGER NOT NULL DEFAULT 0,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    PRIMARY KEY ("Id", "FromTime"),
    CONSTRAINT "FK_VehicleTrips_Vehicles" FOREIGN KEY ("VehicleId")
        REFERENCES "Vehicles"("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_VehicleTrips_Drivers" FOREIGN KEY ("DriverId")
        REFERENCES "Drivers"("Id") ON DELETE SET NULL
) PARTITION BY RANGE ("FromTime");

-- Create monthly partitions for VehicleTrips
CREATE TABLE "VehicleTrips_2025_01" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
CREATE TABLE "VehicleTrips_2025_02" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
CREATE TABLE "VehicleTrips_2025_03" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');
CREATE TABLE "VehicleTrips_2025_04" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-04-01') TO ('2025-05-01');
CREATE TABLE "VehicleTrips_2025_05" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-05-01') TO ('2025-06-01');
CREATE TABLE "VehicleTrips_2025_06" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');
CREATE TABLE "VehicleTrips_2025_07" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');
CREATE TABLE "VehicleTrips_2025_08" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');
CREATE TABLE "VehicleTrips_2025_09" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');
CREATE TABLE "VehicleTrips_2025_10" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');
CREATE TABLE "VehicleTrips_2025_11" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');
CREATE TABLE "VehicleTrips_2025_12" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');
CREATE TABLE "VehicleTrips_2026_01" PARTITION OF "VehicleTrips" FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

-- Create indexes on partitions
CREATE INDEX "IX_VehicleTrips_2025_01_VehicleId_FromTime" ON "VehicleTrips_2025_01" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_02_VehicleId_FromTime" ON "VehicleTrips_2025_02" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_03_VehicleId_FromTime" ON "VehicleTrips_2025_03" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_04_VehicleId_FromTime" ON "VehicleTrips_2025_04" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_05_VehicleId_FromTime" ON "VehicleTrips_2025_05" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_06_VehicleId_FromTime" ON "VehicleTrips_2025_06" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_07_VehicleId_FromTime" ON "VehicleTrips_2025_07" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_08_VehicleId_FromTime" ON "VehicleTrips_2025_08" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_09_VehicleId_FromTime" ON "VehicleTrips_2025_09" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_10_VehicleId_FromTime" ON "VehicleTrips_2025_10" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_11_VehicleId_FromTime" ON "VehicleTrips_2025_11" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2025_12_VehicleId_FromTime" ON "VehicleTrips_2025_12" ("VehicleId", "FromTime" DESC);
CREATE INDEX "IX_VehicleTrips_2026_01_VehicleId_FromTime" ON "VehicleTrips_2026_01" ("VehicleId", "FromTime" DESC);

-- =============================================
-- 7. Tollgates Table
-- =============================================
CREATE TABLE IF NOT EXISTS "Tollgates" (
    "Id" UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "VietmapId" VARCHAR(50) NOT NULL,
    "Name" VARCHAR(200) NOT NULL,
    "Address" VARCHAR(500),
    "X" DOUBLE PRECISION NOT NULL,
    "Y" DOUBLE PRECISION NOT NULL,
    "Heading" INTEGER NOT NULL DEFAULT 0,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    CONSTRAINT "UK_Tollgates_VietmapId" UNIQUE ("VietmapId")
);

CREATE INDEX IF NOT EXISTS "IX_Tollgates_Name" ON "Tollgates" ("Name");

-- =============================================
-- 8. TollgateReports Table
-- =============================================
CREATE TABLE IF NOT EXISTS "TollgateReports" (
    "Id" UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "VehicleId" UUID NOT NULL,
    "DriverId" UUID,
    "FromTollgateId" UUID,
    "ToTollgateId" UUID,
    "FromTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "ToTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "Duration" INTEGER NOT NULL DEFAULT 0,
    "FromTollgateName" VARCHAR(200) NOT NULL,
    "ToTollgateName" VARCHAR(200) NOT NULL,
    "TollgatePrice" DECIMAL(18, 2) NOT NULL DEFAULT 0,
    "FromAddress" VARCHAR(500),
    "ToAddress" VARCHAR(500),
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    CONSTRAINT "FK_TollgateReports_Vehicles" FOREIGN KEY ("VehicleId")
        REFERENCES "Vehicles"("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_TollgateReports_Drivers" FOREIGN KEY ("DriverId")
        REFERENCES "Drivers"("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TollgateReports_FromTollgate" FOREIGN KEY ("FromTollgateId")
        REFERENCES "Tollgates"("Id") ON DELETE NO ACTION,
    CONSTRAINT "FK_TollgateReports_ToTollgate" FOREIGN KEY ("ToTollgateId")
        REFERENCES "Tollgates"("Id") ON DELETE NO ACTION
);

CREATE INDEX IF NOT EXISTS "IX_TollgateReports_VehicleId" ON "TollgateReports" ("VehicleId");
CREATE INDEX IF NOT EXISTS "IX_TollgateReports_FromTime" ON "TollgateReports" ("FromTime");
CREATE INDEX IF NOT EXISTS "IX_TollgateReports_VehicleId_FromTime" ON "TollgateReports" ("VehicleId", "FromTime");

-- =============================================
-- 9. TelematicsViolations Table (PARTITIONED BY ViolationTime - Monthly)
-- =============================================
CREATE TABLE "TelematicsViolations" (
    "Id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "VehicleId" UUID NOT NULL,
    "DriverId" UUID NOT NULL,
    "ViolationTime" TIMESTAMP WITH TIME ZONE NOT NULL,
    "BehaviorType" VARCHAR(50) NOT NULL,
    "Address" VARCHAR(500),
    "Longitude" DOUBLE PRECISION,
    "Latitude" DOUBLE PRECISION,
    "BehaviorDescriptionJson" TEXT,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    PRIMARY KEY ("Id", "ViolationTime"),
    CONSTRAINT "FK_TelematicsViolations_Vehicles" FOREIGN KEY ("VehicleId")
        REFERENCES "Vehicles"("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_TelematicsViolations_Drivers" FOREIGN KEY ("DriverId")
        REFERENCES "Drivers"("Id") ON DELETE NO ACTION
) PARTITION BY RANGE ("ViolationTime");

-- Create monthly partitions for TelematicsViolations
CREATE TABLE "TelematicsViolations_2025_01" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
CREATE TABLE "TelematicsViolations_2025_02" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
CREATE TABLE "TelematicsViolations_2025_03" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');
CREATE TABLE "TelematicsViolations_2025_04" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-04-01') TO ('2025-05-01');
CREATE TABLE "TelematicsViolations_2025_05" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-05-01') TO ('2025-06-01');
CREATE TABLE "TelematicsViolations_2025_06" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');
CREATE TABLE "TelematicsViolations_2025_07" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-07-01') TO ('2025-08-01');
CREATE TABLE "TelematicsViolations_2025_08" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');
CREATE TABLE "TelematicsViolations_2025_09" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');
CREATE TABLE "TelematicsViolations_2025_10" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');
CREATE TABLE "TelematicsViolations_2025_11" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');
CREATE TABLE "TelematicsViolations_2025_12" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');
CREATE TABLE "TelematicsViolations_2026_01" PARTITION OF "TelematicsViolations" FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

-- Create indexes on partitions
CREATE INDEX "IX_TelematicsViolations_2025_01_DriverId_Time" ON "TelematicsViolations_2025_01" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_02_DriverId_Time" ON "TelematicsViolations_2025_02" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_03_DriverId_Time" ON "TelematicsViolations_2025_03" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_04_DriverId_Time" ON "TelematicsViolations_2025_04" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_05_DriverId_Time" ON "TelematicsViolations_2025_05" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_06_DriverId_Time" ON "TelematicsViolations_2025_06" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_07_DriverId_Time" ON "TelematicsViolations_2025_07" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_08_DriverId_Time" ON "TelematicsViolations_2025_08" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_09_DriverId_Time" ON "TelematicsViolations_2025_09" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_10_DriverId_Time" ON "TelematicsViolations_2025_10" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_11_DriverId_Time" ON "TelematicsViolations_2025_11" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2025_12_DriverId_Time" ON "TelematicsViolations_2025_12" ("DriverId", "ViolationTime" DESC);
CREATE INDEX "IX_TelematicsViolations_2026_01_DriverId_Time" ON "TelematicsViolations_2026_01" ("DriverId", "ViolationTime" DESC);

-- =============================================
-- 10. MaintenanceRecords Table
-- =============================================
CREATE TABLE IF NOT EXISTS "MaintenanceRecords" (
    "Id" UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "VehicleId" UUID NOT NULL,
    "MaintenanceTypeId" INTEGER NOT NULL,
    "MaintenanceType" VARCHAR(200) NOT NULL,
    "LastPerformed" TIMESTAMP WITH TIME ZONE,
    "NextDueDate" TIMESTAMP WITH TIME ZONE,
    "DueDistance" BIGINT NOT NULL DEFAULT 0,
    "ActualDistance" BIGINT NOT NULL DEFAULT 0,
    "ActualDistanceDate" TIMESTAMP WITH TIME ZONE,
    "LastMileage" BIGINT NOT NULL DEFAULT 0,
    "Completed" BOOLEAN NOT NULL DEFAULT FALSE,
    "CompletedDate" TIMESTAMP WITH TIME ZONE,
    "DateWarning" TIMESTAMP WITH TIME ZONE,
    "DistanceWarning" BIGINT NOT NULL DEFAULT 0,
    "Amount" DECIMAL(18, 2) NOT NULL DEFAULT 0,
    "Remark" VARCHAR(1000),
    "Inactive" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    CONSTRAINT "FK_MaintenanceRecords_Vehicles" FOREIGN KEY ("VehicleId")
        REFERENCES "Vehicles"("Id") ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS "IX_MaintenanceRecords_VehicleId" ON "MaintenanceRecords" ("VehicleId");
CREATE INDEX IF NOT EXISTS "IX_MaintenanceRecords_MaintenanceTypeId" ON "MaintenanceRecords" ("MaintenanceTypeId");
CREATE INDEX IF NOT EXISTS "IX_MaintenanceRecords_NextDueDate" ON "MaintenanceRecords" ("NextDueDate");
CREATE INDEX IF NOT EXISTS "IX_MaintenanceRecords_VehicleId_Completed" ON "MaintenanceRecords" ("VehicleId", "Completed");

-- =============================================
-- 11. VehiclePerformanceSummaries Table
-- =============================================
CREATE TABLE IF NOT EXISTS "VehiclePerformanceSummaries" (
    "Id" UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "VehicleId" UUID NOT NULL,
    "SummaryDate" TIMESTAMP WITH TIME ZONE NOT NULL,
    "Period" VARCHAR(20) NOT NULL,
    "TotalDistance" BIGINT NOT NULL DEFAULT 0,
    "AverageDistancePerDay" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "MaxDistanceInDay" BIGINT NOT NULL DEFAULT 0,
    "TotalRunTime" INTEGER NOT NULL DEFAULT 0,
    "TotalIdleTime" INTEGER NOT NULL DEFAULT 0,
    "TotalStopTime" INTEGER NOT NULL DEFAULT 0,
    "UtilizationRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "MaxSpeed" INTEGER NOT NULL DEFAULT 0,
    "AverageSpeed" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "OverSpeedCount" INTEGER NOT NULL DEFAULT 0,
    "OverSpeedDuration" INTEGER NOT NULL DEFAULT 0,
    "TotalFuelConsumed" DOUBLE PRECISION,
    "AverageFuelConsumption" DOUBLE PRECISION,
    "FuelEfficiencyScore" DOUBLE PRECISION,
    "TotalTrips" INTEGER NOT NULL DEFAULT 0,
    "AverageTripDistance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "AverageTripDuration" INTEGER NOT NULL DEFAULT 0,
    "UniqueDriversCount" INTEGER NOT NULL DEFAULT 0,
    "MostFrequentDriver" VARCHAR(200),
    "TotalViolations" INTEGER NOT NULL DEFAULT 0,
    "HarshAccelerationCount" INTEGER NOT NULL DEFAULT 0,
    "HarshBrakingCount" INTEGER NOT NULL DEFAULT 0,
    "OverdrivingCount" INTEGER NOT NULL DEFAULT 0,
    "DoorOpenCount" INTEGER NOT NULL DEFAULT 0,
    "DaysActive" INTEGER NOT NULL DEFAULT 0,
    "FirstActivityTime" TIMESTAMP WITH TIME ZONE,
    "LastActivityTime" TIMESTAMP WITH TIME ZONE,
    "TollgateCost" DECIMAL(18, 2),
    "MaintenanceCost" DECIMAL(18, 2),
    "EstimatedOperationalCost" DECIMAL(18, 2),
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    CONSTRAINT "FK_VehiclePerformanceSummaries_Vehicles" FOREIGN KEY ("VehicleId")
        REFERENCES "Vehicles"("Id") ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS "IX_VehiclePerformanceSummaries_VehicleId" ON "VehiclePerformanceSummaries" ("VehicleId");
CREATE INDEX IF NOT EXISTS "IX_VehiclePerformanceSummaries_SummaryDate" ON "VehiclePerformanceSummaries" ("SummaryDate");
CREATE INDEX IF NOT EXISTS "IX_VehiclePerformanceSummaries_Period" ON "VehiclePerformanceSummaries" ("Period");
CREATE UNIQUE INDEX IF NOT EXISTS "UK_VehiclePerformanceSummaries_VehicleId_Date_Period" ON "VehiclePerformanceSummaries" ("VehicleId", "SummaryDate", "Period");

-- =============================================
-- 12. DriverPerformanceSummaries Table
-- =============================================
CREATE TABLE IF NOT EXISTS "DriverPerformanceSummaries" (
    "Id" UUID NOT NULL PRIMARY KEY DEFAULT gen_random_uuid(),
    "DriverId" UUID NOT NULL,
    "SummaryDate" TIMESTAMP WITH TIME ZONE NOT NULL,
    "Period" VARCHAR(20) NOT NULL,
    "TotalShifts" INTEGER NOT NULL DEFAULT 0,
    "TotalWorkHours" INTEGER NOT NULL DEFAULT 0,
    "AverageShiftDuration" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "FirstShiftStart" TIMESTAMP WITH TIME ZONE,
    "LastShiftEnd" TIMESTAMP WITH TIME ZONE,
    "TotalDistance" BIGINT NOT NULL DEFAULT 0,
    "AverageDistancePerShift" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "MaxDistanceInShift" BIGINT NOT NULL DEFAULT 0,
    "AverageSpeed" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "MaxSpeed" INTEGER NOT NULL DEFAULT 0,
    "OverSpeedCount" INTEGER NOT NULL DEFAULT 0,
    "HarshAccelerationCount" INTEGER NOT NULL DEFAULT 0,
    "HarshBrakingCount" INTEGER NOT NULL DEFAULT 0,
    "SafetyScore" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "TotalViolations" INTEGER NOT NULL DEFAULT 0,
    "CriticalViolations" INTEGER NOT NULL DEFAULT 0,
    "OverdrivingDays" INTEGER NOT NULL DEFAULT 0,
    "ContinuousDrivingViolations" INTEGER NOT NULL DEFAULT 0,
    "RestComplianceRate" BOOLEAN NOT NULL DEFAULT FALSE,
    "TotalTrips" INTEGER NOT NULL DEFAULT 0,
    "AverageTripDistance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "IdleTimePercentage" INTEGER NOT NULL DEFAULT 0,
    "FuelEfficiency" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "UniqueVehiclesUsed" INTEGER NOT NULL DEFAULT 0,
    "MostUsedVehiclePlate" VARCHAR(20),
    "PerformanceRating" VARCHAR(50) NOT NULL,
    "Remarks" VARCHAR(500),
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    "CreatedBy" UUID NOT NULL,
    "UpdatedBy" UUID NOT NULL,
    CONSTRAINT "FK_DriverPerformanceSummaries_Drivers" FOREIGN KEY ("DriverId")
        REFERENCES "Drivers"("Id") ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS "IX_DriverPerformanceSummaries_DriverId" ON "DriverPerformanceSummaries" ("DriverId");
CREATE INDEX IF NOT EXISTS "IX_DriverPerformanceSummaries_SummaryDate" ON "DriverPerformanceSummaries" ("SummaryDate");
CREATE INDEX IF NOT EXISTS "IX_DriverPerformanceSummaries_Period" ON "DriverPerformanceSummaries" ("Period");
CREATE UNIQUE INDEX IF NOT EXISTS "UK_DriverPerformanceSummaries_DriverId_Date_Period" ON "DriverPerformanceSummaries" ("DriverId", "SummaryDate", "Period");

-- =============================================
-- 13. gps_locations Table (PARTITIONED BY timestamp - Daily)
-- =============================================
CREATE TABLE "gps_locations" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "device_id" VARCHAR(100) NOT NULL,
    "vehicle_plate" VARCHAR(50),
    "latitude" DOUBLE PRECISION NOT NULL,
    "longitude" DOUBLE PRECISION NOT NULL,
    "altitude" DOUBLE PRECISION,
    "speed" DOUBLE PRECISION,
    "heading" DOUBLE PRECISION,
    "accuracy" DOUBLE PRECISION,
    "timestamp" TIMESTAMP WITH TIME ZONE NOT NULL,
    "battery_level" INTEGER,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP WITH TIME ZONE,
    "created_by" UUID,
    "updated_by" UUID,
    PRIMARY KEY ("id", "timestamp")
) PARTITION BY RANGE ("timestamp");

-- Create daily partitions for gps_locations (current month + next 3 months)
-- January 2025
CREATE TABLE "gps_locations_2025_01_01" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-01') TO ('2025-01-02');
CREATE TABLE "gps_locations_2025_01_02" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-02') TO ('2025-01-03');
CREATE TABLE "gps_locations_2025_01_03" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-03') TO ('2025-01-04');
CREATE TABLE "gps_locations_2025_01_04" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-04') TO ('2025-01-05');
CREATE TABLE "gps_locations_2025_01_05" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-05') TO ('2025-01-06');
CREATE TABLE "gps_locations_2025_01_06" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-06') TO ('2025-01-07');
CREATE TABLE "gps_locations_2025_01_07" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-07') TO ('2025-01-08');
CREATE TABLE "gps_locations_2025_01_08" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-08') TO ('2025-01-09');
CREATE TABLE "gps_locations_2025_01_09" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-09') TO ('2025-01-10');
CREATE TABLE "gps_locations_2025_01_10" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-10') TO ('2025-01-11');
CREATE TABLE "gps_locations_2025_01_11" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-11') TO ('2025-01-12');
CREATE TABLE "gps_locations_2025_01_12" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-12') TO ('2025-01-13');
CREATE TABLE "gps_locations_2025_01_13" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-13') TO ('2025-01-14');
CREATE TABLE "gps_locations_2025_01_14" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-14') TO ('2025-01-15');
CREATE TABLE "gps_locations_2025_01_15" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-15') TO ('2025-01-16');
CREATE TABLE "gps_locations_2025_01_16" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-16') TO ('2025-01-17');
CREATE TABLE "gps_locations_2025_01_17" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-17') TO ('2025-01-18');
CREATE TABLE "gps_locations_2025_01_18" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-18') TO ('2025-01-19');
CREATE TABLE "gps_locations_2025_01_19" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-19') TO ('2025-01-20');
CREATE TABLE "gps_locations_2025_01_20" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-20') TO ('2025-01-21');
CREATE TABLE "gps_locations_2025_01_21" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-21') TO ('2025-01-22');
CREATE TABLE "gps_locations_2025_01_22" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-22') TO ('2025-01-23');
CREATE TABLE "gps_locations_2025_01_23" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-23') TO ('2025-01-24');
CREATE TABLE "gps_locations_2025_01_24" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-24') TO ('2025-01-25');
CREATE TABLE "gps_locations_2025_01_25" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-25') TO ('2025-01-26');
CREATE TABLE "gps_locations_2025_01_26" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-26') TO ('2025-01-27');
CREATE TABLE "gps_locations_2025_01_27" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-27') TO ('2025-01-28');
CREATE TABLE "gps_locations_2025_01_28" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-28') TO ('2025-01-29');
CREATE TABLE "gps_locations_2025_01_29" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-29') TO ('2025-01-30');
CREATE TABLE "gps_locations_2025_01_30" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-30') TO ('2025-01-31');
CREATE TABLE "gps_locations_2025_01_31" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-01-31') TO ('2025-02-01');

-- February 2025 (28 days)
CREATE TABLE "gps_locations_2025_02_01" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-01') TO ('2025-02-02');
CREATE TABLE "gps_locations_2025_02_02" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-02') TO ('2025-02-03');
CREATE TABLE "gps_locations_2025_02_03" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-03') TO ('2025-02-04');
CREATE TABLE "gps_locations_2025_02_04" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-04') TO ('2025-02-05');
CREATE TABLE "gps_locations_2025_02_05" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-05') TO ('2025-02-06');
CREATE TABLE "gps_locations_2025_02_06" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-06') TO ('2025-02-07');
CREATE TABLE "gps_locations_2025_02_07" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-07') TO ('2025-02-08');
CREATE TABLE "gps_locations_2025_02_08" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-08') TO ('2025-02-09');
CREATE TABLE "gps_locations_2025_02_09" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-09') TO ('2025-02-10');
CREATE TABLE "gps_locations_2025_02_10" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-10') TO ('2025-02-11');
CREATE TABLE "gps_locations_2025_02_11" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-11') TO ('2025-02-12');
CREATE TABLE "gps_locations_2025_02_12" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-12') TO ('2025-02-13');
CREATE TABLE "gps_locations_2025_02_13" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-13') TO ('2025-02-14');
CREATE TABLE "gps_locations_2025_02_14" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-14') TO ('2025-02-15');
CREATE TABLE "gps_locations_2025_02_15" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-15') TO ('2025-02-16');
CREATE TABLE "gps_locations_2025_02_16" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-16') TO ('2025-02-17');
CREATE TABLE "gps_locations_2025_02_17" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-17') TO ('2025-02-18');
CREATE TABLE "gps_locations_2025_02_18" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-18') TO ('2025-02-19');
CREATE TABLE "gps_locations_2025_02_19" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-19') TO ('2025-02-20');
CREATE TABLE "gps_locations_2025_02_20" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-20') TO ('2025-02-21');
CREATE TABLE "gps_locations_2025_02_21" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-21') TO ('2025-02-22');
CREATE TABLE "gps_locations_2025_02_22" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-22') TO ('2025-02-23');
CREATE TABLE "gps_locations_2025_02_23" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-23') TO ('2025-02-24');
CREATE TABLE "gps_locations_2025_02_24" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-24') TO ('2025-02-25');
CREATE TABLE "gps_locations_2025_02_25" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-25') TO ('2025-02-26');
CREATE TABLE "gps_locations_2025_02_26" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-26') TO ('2025-02-27');
CREATE TABLE "gps_locations_2025_02_27" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-27') TO ('2025-02-28');
CREATE TABLE "gps_locations_2025_02_28" PARTITION OF "gps_locations" FOR VALUES FROM ('2025-02-28') TO ('2025-03-01');

-- Create indexes on representative partitions (can be applied to all as needed)
CREATE INDEX "idx_gps_2025_01_01_device_ts" ON "gps_locations_2025_01_01" ("device_id", "timestamp" DESC);
CREATE INDEX "idx_gps_2025_01_01_vehicle_ts" ON "gps_locations_2025_01_01" ("vehicle_plate", "timestamp" DESC);
CREATE INDEX "idx_gps_2025_02_01_device_ts" ON "gps_locations_2025_02_01" ("device_id", "timestamp" DESC);
CREATE INDEX "idx_gps_2025_02_01_vehicle_ts" ON "gps_locations_2025_02_01" ("vehicle_plate", "timestamp" DESC);

-- =============================================
-- PARTITION MANAGEMENT FUNCTIONS
-- =============================================

-- Function to create next month partition for VehicleStatuses
CREATE OR REPLACE FUNCTION create_vehiclestatus_partition()
RETURNS void AS $$
DECLARE
    partition_date DATE;
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    -- Get the first day of next month
    partition_date := DATE_TRUNC('month', CURRENT_DATE + INTERVAL '1 month');
    partition_name := 'VehicleStatuses_' || TO_CHAR(partition_date, 'YYYY_MM');
    start_date := partition_date;
    end_date := partition_date + INTERVAL '1 month';

    -- Check if partition already exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_class WHERE relname = LOWER(partition_name)
    ) THEN
        EXECUTE format('CREATE TABLE %I PARTITION OF "VehicleStatuses" FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date);
        EXECUTE format('CREATE INDEX %I ON %I ("VehicleId", "GpsTime" DESC)',
            'IX_' || partition_name || '_VehicleId_GpsTime', partition_name);
        RAISE NOTICE 'Created partition: %', partition_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to create next day partition for gps_locations
CREATE OR REPLACE FUNCTION create_gpslocation_partition()
RETURNS void AS $$
DECLARE
    partition_date DATE;
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    -- Get tomorrow's date
    partition_date := CURRENT_DATE + INTERVAL '1 day';
    partition_name := 'gps_locations_' || TO_CHAR(partition_date, 'YYYY_MM_DD');
    start_date := partition_date;
    end_date := partition_date + INTERVAL '1 day';

    -- Check if partition already exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_class WHERE relname = partition_name
    ) THEN
        EXECUTE format('CREATE TABLE %I PARTITION OF "gps_locations" FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date);
        EXECUTE format('CREATE INDEX %I ON %I ("device_id", "timestamp" DESC)',
            'idx_' || partition_name || '_device_ts', partition_name);
        EXECUTE format('CREATE INDEX %I ON %I ("vehicle_plate", "timestamp" DESC)',
            'idx_' || partition_name || '_vehicle_ts', partition_name);
        RAISE NOTICE 'Created partition: %', partition_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to create next month partition for VehicleTrips
CREATE OR REPLACE FUNCTION create_vehicletrip_partition()
RETURNS void AS $$
DECLARE
    partition_date DATE;
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    partition_date := DATE_TRUNC('month', CURRENT_DATE + INTERVAL '1 month');
    partition_name := 'VehicleTrips_' || TO_CHAR(partition_date, 'YYYY_MM');
    start_date := partition_date;
    end_date := partition_date + INTERVAL '1 month';

    IF NOT EXISTS (
        SELECT 1 FROM pg_class WHERE relname = LOWER(partition_name)
    ) THEN
        EXECUTE format('CREATE TABLE %I PARTITION OF "VehicleTrips" FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date);
        EXECUTE format('CREATE INDEX %I ON %I ("VehicleId", "FromTime" DESC)',
            'IX_' || partition_name || '_VehicleId_FromTime', partition_name);
        RAISE NOTICE 'Created partition: %', partition_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to create next month partition for DriverShifts
CREATE OR REPLACE FUNCTION create_drivershift_partition()
RETURNS void AS $$
DECLARE
    partition_date DATE;
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    partition_date := DATE_TRUNC('month', CURRENT_DATE + INTERVAL '1 month');
    partition_name := 'DriverShifts_' || TO_CHAR(partition_date, 'YYYY_MM');
    start_date := partition_date;
    end_date := partition_date + INTERVAL '1 month';

    IF NOT EXISTS (
        SELECT 1 FROM pg_class WHERE relname = LOWER(partition_name)
    ) THEN
        EXECUTE format('CREATE TABLE %I PARTITION OF "DriverShifts" FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date);
        EXECUTE format('CREATE INDEX %I ON %I ("DriverId", "FromTime" DESC)',
            'IX_' || partition_name || '_DriverId_FromTime', partition_name);
        RAISE NOTICE 'Created partition: %', partition_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to create next month partition for TelematicsViolations
CREATE OR REPLACE FUNCTION create_violation_partition()
RETURNS void AS $$
DECLARE
    partition_date DATE;
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    partition_date := DATE_TRUNC('month', CURRENT_DATE + INTERVAL '1 month');
    partition_name := 'TelematicsViolations_' || TO_CHAR(partition_date, 'YYYY_MM');
    start_date := partition_date;
    end_date := partition_date + INTERVAL '1 month';

    IF NOT EXISTS (
        SELECT 1 FROM pg_class WHERE relname = LOWER(partition_name)
    ) THEN
        EXECUTE format('CREATE TABLE %I PARTITION OF "TelematicsViolations" FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date);
        EXECUTE format('CREATE INDEX %I ON %I ("ViolationTime" DESC)',
            'IX_' || partition_name || '_ViolationTime', partition_name);
        RAISE NOTICE 'Created partition: %', partition_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to drop old gps_locations partitions (older than 90 days)
CREATE OR REPLACE FUNCTION drop_old_gpslocation_partitions()
RETURNS void AS $$
DECLARE
    partition_record RECORD;
    cutoff_date DATE;
BEGIN
    cutoff_date := CURRENT_DATE - INTERVAL '90 days';

    FOR partition_record IN
        SELECT tablename FROM pg_tables
        WHERE schemaname = 'public'
        AND tablename LIKE 'gps_locations_%'
        AND tablename ~ '^gps_locations_[0-9]{4}_[0-9]{2}_[0-9]{2}$'
    LOOP
        DECLARE
            partition_date DATE;
        BEGIN
            -- Extract date from partition name (gps_locations_YYYY_MM_DD)
            partition_date := TO_DATE(
                SUBSTRING(partition_record.tablename FROM 15 FOR 10),
                'YYYY_MM_DD'
            );

            IF partition_date < cutoff_date THEN
                EXECUTE format('DROP TABLE IF EXISTS %I', partition_record.tablename);
                RAISE NOTICE 'Dropped old partition: %', partition_record.tablename;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error processing partition %: %', partition_record.tablename, SQLERRM;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to drop old monthly partitions (older than 13 months)
CREATE OR REPLACE FUNCTION drop_old_monthly_partitions()
RETURNS void AS $$
DECLARE
    partition_record RECORD;
    cutoff_date DATE;
    table_prefix TEXT;
BEGIN
    cutoff_date := DATE_TRUNC('month', CURRENT_DATE - INTERVAL '13 months');

    -- Process each partitioned table
    FOR table_prefix IN
        SELECT unnest(ARRAY['VehicleStatuses', 'VehicleTrips', 'DriverShifts', 'TelematicsViolations'])
    LOOP
        FOR partition_record IN
            SELECT tablename FROM pg_tables
            WHERE schemaname = 'public'
            AND tablename LIKE LOWER(table_prefix) || '_%'
            AND tablename ~ ('^' || LOWER(table_prefix) || '_[0-9]{4}_[0-9]{2}$')
        LOOP
            DECLARE
                partition_date DATE;
            BEGIN
                -- Extract date from partition name (TableName_YYYY_MM)
                partition_date := TO_DATE(
                    SUBSTRING(partition_record.tablename FROM LENGTH(table_prefix) + 2 FOR 7),
                    'YYYY_MM'
                );

                IF partition_date < cutoff_date THEN
                    EXECUTE format('DROP TABLE IF EXISTS %I', partition_record.tablename);
                    RAISE NOTICE 'Dropped old partition: %', partition_record.tablename;
                END IF;
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Error processing partition %: %', partition_record.tablename, SQLERRM;
            END;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Master function to create all necessary partitions
CREATE OR REPLACE FUNCTION maintain_partitions()
RETURNS void AS $$
BEGIN
    -- Create future partitions
    PERFORM create_vehiclestatus_partition();
    PERFORM create_gpslocation_partition();
    PERFORM create_vehicletrip_partition();
    PERFORM create_drivershift_partition();
    PERFORM create_violation_partition();

    -- Drop old partitions
    PERFORM drop_old_gpslocation_partitions();
    PERFORM drop_old_monthly_partitions();

    RAISE NOTICE 'Partition maintenance completed successfully';
END;
$$ LANGUAGE plpgsql;

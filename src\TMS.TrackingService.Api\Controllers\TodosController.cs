using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.TrackingService.Application.Features.Todos.Commands.CreateTodo;
using TMS.TrackingService.Application.Features.Todos.Commands.UpdateTodo;
using TMS.TrackingService.Application.Features.Todos.Queries.GetTodos;
using TMS.TrackingService.Application.Features.Todos.Queries.GetTodoById;
using TMS.TrackingService.Contracts.Todos;

namespace TMS.TrackingService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/[controller]")]
[Produces("application/json")]
public class TodosController : ControllerBase
{
    private readonly IMediator _mediator;

    public TodosController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Get all todos
    /// </summary>
    /// <returns>List of todos</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<TodoResponse>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TodoResponse>>> GetTodos()
    {
        var todos = await _mediator.Send(new GetTodosQuery());
        return Ok(todos);
    }

    /// <summary>
    /// Get todo by ID
    /// </summary>
    /// <param name="id">Todo ID</param>
    /// <returns>Todo details</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(TodoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TodoResponse>> GetTodo(Guid id)
    {
        var todo = await _mediator.Send(new GetTodoByIdQuery(id));
        
        if (todo == null)
            return NotFound($"Todo with ID {id} not found");
            
        return Ok(todo);
    }

    /// <summary>
    /// Create a new todo
    /// </summary>
    /// <param name="request">Todo creation data</param>
    /// <returns>Created todo</returns>
    [HttpPost]
    [ProducesResponseType(typeof(TodoResponse), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TodoResponse>> CreateTodo([FromBody] CreateTodoRequest request)
    {
        var command = new CreateTodoCommand(request.Title, request.Description);
        var todo = await _mediator.Send(command);
        
        return CreatedAtAction(nameof(GetTodo), new { id = todo.Id }, todo);
    }

    /// <summary>
    /// Update an existing todo
    /// </summary>
    /// <param name="id">Todo ID</param>
    /// <param name="request">Todo update data</param>
    /// <returns>Updated todo</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(TodoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TodoResponse>> UpdateTodo(Guid id, [FromBody] UpdateTodoRequest request)
    {
        try
        {
            var command = new UpdateTodoCommand(id, request.Title, request.Description, request.IsCompleted);
            var todo = await _mediator.Send(command);
            
            return Ok(todo);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// Mark todo as completed
    /// </summary>
    /// <param name="id">Todo ID</param>
    /// <returns>Updated todo</returns>
    [HttpPatch("{id:guid}/complete")]
    [ProducesResponseType(typeof(TodoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TodoResponse>> CompleteTodo(Guid id)
    {
        try
        {
            var existingTodo = await _mediator.Send(new GetTodoByIdQuery(id));
            if (existingTodo == null)
                return NotFound($"Todo with ID {id} not found");

            var command = new UpdateTodoCommand(id, existingTodo.Title, existingTodo.Description, true);
            var todo = await _mediator.Send(command);
            
            return Ok(todo);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// Delete a todo
    /// </summary>
    /// <param name="id">Todo ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteTodo(Guid id)
    {
        // You'll need to create DeleteTodoCommand and Handler
        return NoContent();
    }
}

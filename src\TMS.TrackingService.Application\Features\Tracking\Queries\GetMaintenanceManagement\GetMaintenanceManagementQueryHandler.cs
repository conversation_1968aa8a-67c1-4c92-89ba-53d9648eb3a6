using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.TrackingService.Contracts.Vietmap;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Features.Tracking.Queries.GetMaintenanceManagement;

public class GetMaintenanceManagementQueryHandler : IRequestHandler<GetMaintenanceManagementQuery, MaintenanceManagementResponse>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<GetMaintenanceManagementQueryHandler> _logger;

    public GetMaintenanceManagementQueryHandler(ApplicationDbContext dbContext, IMapper mapper, ILogger<GetMaintenanceManagementQueryHandler> logger)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<MaintenanceManagementResponse> Handle(GetMaintenanceManagementQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var query = _dbContext.MaintenanceRecords
                .AsNoTracking()
                .Include(mr => mr.Vehicle)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(request.Plate))
            {
                query = query.Where(mr => mr.Vehicle.Plate.Contains(request.Plate));
            }

            if (request.VehicleGroupId.HasValue)
            {
                query = query.Where(mr => mr.Vehicle.GroupId == request.VehicleGroupId.Value);
            }

            if (request.Type.HasValue)
            {
                query = query.Where(mr => mr.MaintenanceTypeId == request.Type.Value);
            }

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination
            var page = request.Page ?? 1;
            var pageSize = request.PageSize ?? 20;
            var skip = (page - 1) * pageSize;

            var records = await query
                .OrderByDescending(mr => mr.CreatedAt)
                .Skip(skip)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            var recordDtos = _mapper.Map<List<MaintenanceDto>>(records);

            return new MaintenanceManagementResponse
            {
                Data = recordDtos,
                Total = totalCount
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching maintenance management from database");
            return new MaintenanceManagementResponse
            {
                Data = new List<MaintenanceDto>(),
                Total = 0
            };
        }
    }
}

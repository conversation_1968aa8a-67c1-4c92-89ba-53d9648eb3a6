﻿using System.Reflection;
using FluentValidation.AspNetCore;
using Quartz;
using Refit;
using TMS.SharedKernel.Authentication;
using TMS.SharedKernel.Utilities;
using TMS.TrackingService.Api;
using TMS.TrackingService.ApiClient;
using TMS.TrackingService.Application;
using TMS.TrackingService.Application.Job;
using TMS.TrackingService.Infra;
using TMS.TrackingService.Infra.Data;
using TMS.TruckService.Application;

internal class Program
{
    private static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        var otlpEndpoint = builder.Configuration["OpenTelemetry:OtlpEndpoint"] ?? String.Empty;
        var otlpEndpointKey = builder.Configuration["OpenTelemetry:Key"] ?? String.Empty;
        var env = builder.Configuration["environment"] ?? "Development";
        var serviceName = builder.Environment.ApplicationName; //"TMS.TrackingService.Api";
        var serviceVersion = "v1";

        builder.AddBaseRequired(otlpEndpoint, otlpEndpointKey, env, serviceName, serviceVersion);

        // Add Application and Infrastructure services
        builder.Services.AddApplication();
        builder.Services.AddInfrastructure(builder.Configuration);

        // Add inherit DbContext for design time migrations
        builder.Services.AddDerivedDbContext<ApplicationDbContext>();

        // you could add Polly here to handle HTTP 429 / HTTP 503 etc
        builder.Services.AddPolicyBasedAuthorization(PermissionDefinition.AllPermissions);

        // Register Vietmap Authentication Handler
        builder.Services.AddTransient<VietmapAuthenticationHandler>();

        // Register Vietmap Tracking API with authentication handler
        builder.Services.AddRefitClient<IVietmapTrackingApi>()
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri("https://client-api.quanlyxe.vn");
                c.Timeout = TimeSpan.FromMinutes(5); // Increase timeout for bulk operations
            })
            .AddHttpMessageHandler<VietmapAuthenticationHandler>();

        var connString = builder.Configuration.GetConnectionString("TrackingDBConnection") ?? "";
        builder.Services.AddClusteredQuartz(
            connString,
            schedulerName: $"{serviceName}-scheduler");

        // Quartz cron: seconds minutes hours day-of-month month day-of-week [year]
        // "0 */5 * ? * *" = every 5 minutes (? means "no specific value" for day-of-month)
        var pullingCronSchedule = builder.Configuration["Vietmap:PullingCron"] ?? "0 */5 * ? * *";
        var analysisCronSchedule = builder.Configuration["Vietmap:AnalysisCron"] ?? "0 0 1 ? * *"; // Daily at 1 AM
        var analysisProcessPeriods = builder.Configuration["Vietmap:AnalysisProcessPeriods"] ?? "Daily";
        var analysisLookbackDays = builder.Configuration.GetValue<int>("Vietmap:AnalysisLookbackDays", 1);

        builder.Services.AddQuartz(q =>
        {
            // VietmapPullingJob - Data synchronization
            var pullingJobKey = new JobKey("VietmapPullingJob");
            q.AddJob<VietmapPullingJob>(opts => opts.WithIdentity(pullingJobKey));
            q.AddTrigger(opts => opts
                .ForJob(pullingJobKey)
                .WithIdentity("VietmapPullingJob-trigger")
                .WithCronSchedule(pullingCronSchedule));

            // VietmapSummarizedProcessingJob - Data analysis
            var analysisJobKey = new JobKey("VietmapSummarizedProcessingJob");
            q.AddJob<VietmapSummarizedProcessingJob>(opts => opts
                .WithIdentity(analysisJobKey)
                .UsingJobData("ProcessPeriods", analysisProcessPeriods)
                .UsingJobData("LookbackDays", $"{analysisLookbackDays}"));

            q.AddTrigger(opts => opts
                .ForJob(analysisJobKey)
                .WithIdentity("VietmapSummarizedProcessingJob-trigger")
                .WithCronSchedule(analysisCronSchedule));
        });

        var app = builder.Build();

        // Auto-create database
        app.EnsureDatabaseCreated<ApplicationDbContext>();
        var schemaOperations = new List<(string TableName, string Indicator)>
        {
            ("qrtz_job_details", "Default"),
            ("qrtz_job_details", "Quartz"),
        };
        app.EnsureAllDatabaseSchemasCreated(connectionString: connString, schemaOperations);

        app.UseBaseRequired(env, serviceName, serviceVersion);


        app.Run();
    }
}

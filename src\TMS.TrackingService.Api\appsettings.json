{
  "ConnectionStrings": {
    "TrackingDBConnection": "Host=localhost;Database=TrackingDB;Username=********;Password=********"
  },
  "Cors": {
  "AllowedOrigins": [
    "*"
  ],
  "AllowedMethods": [
    "GET",
    "POST",
    "PUT",
    "DELETE",
    "PATCH"
  ],
  "AllowedHeaders": [
    "*"
  ],
  "AllowCredentials": true
},
  "DefaultKey": "<set>",
  "Targets": [
    {
      "Name": "TMS.DriverService.Api",
      "Url": "https://localhost:7078"
    }
  ],
  "Identity": {
    "Issuer": "247-Eco-System",
    "Audience": "tms-api",
    "Key": "<set>"
  },
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Information",
        "System": "Information",
        "Microsoft.EntityFrameworkCore.Database.Command": "Information"
      }
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "Console",
              "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"
            }
          ]
        }
      }
    ]
  },
  "environment": "development",
  "OpenTelemetry": {
    "OtlpEndpoint": "http://localhost:4318", // protobuf
    "Key": "<set>"
  },
  "Vietmap": {
    "apiKey": "<set>",
    "PullingCron": "0 */1 * ? * *",
    "TrackingProcessing": "0 */5 * ? * *",
    "AnalysisCron": "0 0 1 ? * *",
    "AnalysisProcessPeriods": "Daily",
    "AnalysisLookbackDays": 1
  }
}

using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.TrackingService.Contracts.Vietmap;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Features.Tracking.Queries.GetDailyReports;

public class GetDailyReportsQueryHandler : IRequestHandler<GetDailyReportsQuery, List<DailyReportDto>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<GetDailyReportsQueryHandler> _logger;

    public GetDailyReportsQueryHandler(ApplicationDbContext dbContext, IMapper mapper, ILogger<GetDailyReportsQueryHandler> logger)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<DailyReportDto>> Handle(GetDailyReportsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            // Parse date range
            if (!DateTime.TryParse(request.From, out var fromDate) || !DateTime.TryParse(request.To, out var toDate))
            {
                _logger.LogWarning("Invalid date format: From={From}, To={To}", request.From, request.To);
                return new List<DailyReportDto>();
            }

            // Get vehicle by Vietmap ID
            var vehicle = await _dbContext.Vehicles
                .AsNoTracking()
                .FirstOrDefaultAsync(v => v.VietmapId == request.VietmapVehicleId, cancellationToken);

            if (vehicle == null)
            {
                _logger.LogWarning("Vehicle with Vietmap ID {VietmapId} not found", request.VietmapVehicleId);
                return new List<DailyReportDto>();
            }

            // Get daily reports for the date range
            var reports = await _dbContext.DailyReports
                .AsNoTracking()
                .Where(dr => dr.VehicleId == vehicle.Id && dr.Date >= fromDate && dr.Date <= toDate)
                .OrderBy(dr => dr.Date)
                .ToListAsync(cancellationToken);

            return _mapper.Map<List<DailyReportDto>>(reports);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching daily reports from database");
            return new List<DailyReportDto>();
        }
    }
}

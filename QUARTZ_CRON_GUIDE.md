# Quartz.NET Cron Expression Guide

## Format

Quartz.NET cron expressions have **6 or 7 fields**:

```
┌────────── second (0 - 59)
│ ┌──────── minute (0 - 59)
│ │ ┌────── hour (0 - 23)
│ │ │ ┌──── day of month (1 - 31)
│ │ │ │ ┌── month (1 - 12 or JAN-DEC)
│ │ │ │ │ ┌ day of week (0 - 6 or SUN-SAT)
│ │ │ │ │ │
* * * * * *
```

## Special Characters

- `*` - All values (every)
- `?` - No specific value (used in day-of-month or day-of-week)
- `-` - Range (e.g., `10-12` = 10, 11, 12)
- `,` - List (e.g., `MON,WED,FRI`)
- `/` - Increment (e.g., `*/5` = every 5)
- `L` - Last (e.g., `L` in day-of-month = last day of month)
- `W` - Weekday (e.g., `15W` = nearest weekday to 15th)
- `#` - Nth day (e.g., `FRI#3` = 3rd Friday of month)

## Important Rule

⚠️ **You CANNOT specify both day-of-month AND day-of-week**

- If you specify day-of-month, use `?` for day-of-week
- If you specify day-of-week, use `?` for day-of-month

### ✅ Correct Examples
```
0 0 12 ? * MON        - Every Monday at noon (? for day-of-month)
0 0 0 15 * ?          - 15th of every month at midnight (? for day-of-week)
0 */5 * ? * *         - Every 5 minutes (? for day-of-month)
```

### ❌ Incorrect Examples
```
0 0 12 * * MON        - ERROR! Both day-of-month (*) and day-of-week (MON)
0 */5 * * * ?         - ERROR! Both day-of-month (*) and day-of-week (?)
```

## Common Vietmap Tracking Schedules

### High Frequency (Real-time tracking)
```json
{
  "Vietmap:PullingCron": "0 */1 * ? * *"  // Every 1 minute
}
```

### Standard Frequency (Recommended)
```json
{
  "Vietmap:PullingCron": "0 */5 * ? * *"  // Every 5 minutes
}
```

### Low Frequency (Master data sync only)
```json
{
  "Vietmap:PullingCron": "0 0 * ? * *"    // Every hour
}
```

### Daily Reports Only
```json
{
  "Vietmap:PullingCron": "0 0 0 ? * *"    // Once daily at midnight
}
```

## Complete Examples

| Description | Cron Expression | Explanation |
|-------------|----------------|-------------|
| Every 1 minute | `0 */1 * ? * *` | s m h dom mon dow |
| Every 5 minutes | `0 */5 * ? * *` | Every 5 min, any hour/day |
| Every 10 minutes | `0 */10 * ? * *` | Every 10 min |
| Every 15 minutes | `0 */15 * ? * *` | Every 15 min |
| Every 30 minutes | `0 */30 * ? * *` | Every 30 min |
| Every hour | `0 0 * ? * *` | At minute 0 of every hour |
| Every 3 hours | `0 0 */3 ? * *` | At minute 0, every 3 hours |
| Every 6 hours | `0 0 */6 ? * *` | 00:00, 06:00, 12:00, 18:00 |
| Twice daily | `0 0 0,12 ? * *` | Midnight and noon |
| Once daily at midnight | `0 0 0 ? * *` | 00:00:00 every day |
| Once daily at 2 AM | `0 0 2 ? * *` | 02:00:00 every day |
| Once daily at 9 AM | `0 0 9 ? * *` | 09:00:00 every day |
| Weekdays at 9 AM | `0 0 9 ? * MON-FRI` | 09:00 Mon-Fri |
| Weekends at 10 AM | `0 0 10 ? * SAT,SUN` | 10:00 Sat & Sun |
| Every Monday at noon | `0 0 12 ? * MON` | 12:00:00 every Monday |
| First day of month | `0 0 0 1 * ?` | 00:00 on 1st of month |
| Last day of month | `0 0 0 L * ?` | 00:00 on last day |
| Every 5 sec (testing) | `*/5 * * ? * *` | Every 5 seconds |
| Every 30 sec (testing) | `*/30 * * ? * *` | Every 30 seconds |

## Vietmap Use Cases

### Real-time Fleet Tracking
```json
{
  "Vietmap:PullingCron": "0 */1 * ? * *"
}
```
- Updates every minute
- Best for: Active fleet monitoring, live dashboards
- Cost: High API usage

### Standard Fleet Tracking
```json
{
  "Vietmap:PullingCron": "0 */5 * ? * *"
}
```
- Updates every 5 minutes
- Best for: Regular fleet management
- Cost: Moderate API usage

### Light Tracking
```json
{
  "Vietmap:PullingCron": "0 */15 * ? * *"
}
```
- Updates every 15 minutes
- Best for: Non-critical tracking
- Cost: Low API usage

### Master Data Sync Only
```json
{
  "Vietmap:PullingCron": "0 0 */6 ? * *"
}
```
- Updates every 6 hours
- Best for: Vehicles, drivers, tollgates only
- Cost: Minimal API usage

### Reports Generation
```json
{
  "Vietmap:PullingCron": "0 0 1 ? * *"
}
```
- Updates once daily at 1 AM
- Best for: Daily reports generation
- Cost: Very low API usage

## Testing Expressions

### Quick Test Schedule (Development)
```json
{
  "Vietmap:PullingCron": "0 */1 * ? * *"  // Every minute for testing
}
```

### Manual Trigger (Disable Auto)
```json
{
  "Vietmap:PullingCron": "0 0 0 1 1 ? 2099"  // Never runs (year 2099)
}
```

## Debugging

### Check Current Schedule
```sql
SELECT * FROM QRTZ_TRIGGERS WHERE TRIGGER_NAME = 'VietmapPullingJob-trigger';
```

### View Next Fire Time
```sql
SELECT
    TRIGGER_NAME,
    TRIGGER_STATE,
    CONVERT(datetime, DATEADD(s, NEXT_FIRE_TIME/1000, '1970-01-01')) AS NextFireTime
FROM QRTZ_TRIGGERS
WHERE TRIGGER_NAME = 'VietmapPullingJob-trigger';
```

## Best Practices

1. **Start Conservative**: Begin with longer intervals (15-30 min) and reduce if needed
2. **Consider API Limits**: Too frequent calls may hit rate limits
3. **Match Business Needs**: Real-time tracking isn't always necessary
4. **Monitor Execution Time**: Ensure job completes before next trigger
5. **Use `?` Correctly**: Always use `?` for either day-of-month or day-of-week

## Common Errors

### ❌ Both day-of-month and day-of-week specified
```
Error: Support for specifying both a day-of-week AND a day-of-month parameter is not implemented.
Fix: Use ? for one of them
```

### ❌ Invalid field count
```
Error: Cron expression must have 6 or 7 fields
Fix: Check your expression has correct number of fields
```

### ❌ Invalid range
```
Error: Invalid cron expression
Fix: Ensure ranges are valid (0-59 for seconds/minutes, 0-23 for hours, etc.)
```

## References

- [Quartz.NET Documentation](https://www.quartz-scheduler.net/documentation/)
- [Cron Expression Generator](https://www.freeformatter.com/cron-expression-generator-quartz.html)
- [Cron Expression Validator](https://cronexpressiondescriptor.azurewebsites.net/)

# Database Partitioning Strategy for TMS Tracking Service

## Executive Summary

This document analyzes the data persistence layer of the TMS Tracking Service and provides partitioning recommendations for tables that will experience high data volume growth. The analysis is based on data insertion patterns, query characteristics, and retention requirements.

---

## Table Analysis & Data Volume Assessment

### 1. HIGH-VOLUME TABLES (Require Partitioning)

#### 1.1 **VehicleStatuses** (GPS Tracking Data)
**Current Configuration:** `D:\01. Projects\2025\Core product management system (CPMS)\TMS\TMS.TrackingService\src\TMS.TrackingService.Infra\Data\Configurations\VehicleStatusConfiguration.cs:11`

**Characteristics:**
- **Data Volume:** CRITICAL - Highest volume table
- **Insertion Rate:** Continuous (every 1-30 seconds per vehicle)
- **Growth Rate:** ~2,880 - 86,400 records per vehicle per day (assuming 30-second to 1-second intervals)
- **Data Pattern:** Time-series data with GPS coordinates
- **Retention:** Historical data (typically 6-12 months active, archive older)

**Current Indexes:**
- `VehicleId` (single column)
- `GpsTime` (single column)
- `VehicleId + GpsTime` (composite)

**Query Patterns:**
- Latest status by vehicle: `WHERE VehicleId = X ORDER BY GpsTime DESC LIMIT 1`
- Historical tracking: `WHERE VehicleId = X AND GpsTime BETWEEN date1 AND date2`
- Real-time monitoring: Recent data within last few minutes

**Estimated Annual Growth (100 vehicles, 30-sec interval):**
- Records per day: ~288,000
- Records per month: ~8,640,000
- Records per year: ~105,120,000

**⚠️ CRITICAL IMPACT:** Without partitioning, queries will degrade significantly after 6-12 months.

---

#### 1.2 **GpsLocations** (Raw GPS Data from Mobile Devices)
**Current Configuration:** `D:\01. Projects\2025\Core product management system (CPMS)\TMS\TMS.TrackingService\src\TMS.TrackingService.Infra\Data\Configurations\GpsLocationConfiguration.cs:11`

**Characteristics:**
- **Data Volume:** CRITICAL - Very high volume
- **Insertion Rate:** Continuous (1-second intervals from mobile apps)
- **Growth Rate:** ~86,400 records per device per day
- **Data Pattern:** Time-series geolocation data
- **Retention:** Short-term (typically 7-30 days before archival)

**Current Indexes:**
- `DeviceId`
- `VehiclePlate`
- `Timestamp`
- `CreatedAt`
- `DeviceId + Timestamp` (composite)
- `VehiclePlate + Timestamp` (composite)

**Query Patterns:**
- Recent locations: `WHERE DeviceId = X AND Timestamp > NOW() - INTERVAL '1 hour'`
- Track device path: `WHERE DeviceId = X AND Timestamp BETWEEN date1 AND date2`
- Vehicle tracking: `WHERE VehiclePlate = X AND Timestamp BETWEEN date1 AND date2`

**Estimated Annual Growth (200 devices, 1-sec interval):**
- Records per day: ~17,280,000
- Records per month: ~518,400,000
- Records per year: ~6,220,800,000 (6.2 billion!)

**⚠️ CRITICAL IMPACT:** Without partitioning, this table will become unmanageable within 3-6 months.

---

#### 1.3 **VehicleTrips** (Trip Records)
**Current Configuration:** Referenced in domain entities

**Characteristics:**
- **Data Volume:** HIGH
- **Insertion Rate:** Multiple times per day per vehicle
- **Growth Rate:** ~5-20 trips per vehicle per day
- **Data Pattern:** Time-series with from/to timestamps
- **Retention:** Long-term (1-3 years active)

**Query Patterns:**
- Vehicle trip history: `WHERE VehicleId = X AND FromTime BETWEEN date1 AND date2`
- Driver trip reports: `WHERE DriverId = X AND FromTime BETWEEN date1 AND date2`
- Date-range trip reports

**Estimated Annual Growth (100 vehicles, 10 trips/day):**
- Records per day: ~1,000
- Records per month: ~30,000
- Records per year: ~365,000

**Impact:** Moderate - Will benefit from partitioning after 1-2 years.

---

#### 1.4 **DriverShifts** (Driver Work Sessions)
**Characteristics:**
- **Data Volume:** MODERATE-HIGH
- **Insertion Rate:** 1-3 shifts per driver per day
- **Growth Rate:** Depends on driver count
- **Data Pattern:** Time-series (FromTime/ToTime)
- **Retention:** Long-term (2-3 years for compliance)

**Query Patterns:**
- Driver shift history: `WHERE DriverId = X AND FromTime BETWEEN date1 AND date2`
- Daily shift reports: `WHERE DATE(FromTime) = specific_date`

**Estimated Annual Growth (150 drivers, 2 shifts/day):**
- Records per day: ~300
- Records per month: ~9,000
- Records per year: ~109,500

**Impact:** Moderate - Will benefit from partitioning after 2-3 years.

---

#### 1.5 **TelematicsViolations** (Driving Violations)
**Characteristics:**
- **Data Volume:** MODERATE
- **Insertion Rate:** Variable (depends on driving behavior)
- **Growth Rate:** ~5-50 violations per vehicle per day
- **Data Pattern:** Time-series (ViolationTime)
- **Retention:** Long-term (3-5 years for compliance/auditing)

**Query Patterns:**
- Driver violation history: `WHERE DriverId = X AND ViolationTime BETWEEN date1 AND date2`
- Vehicle violations: `WHERE VehicleId = X AND ViolationTime BETWEEN date1 AND date2`
- Violation type analysis

**Estimated Annual Growth (100 vehicles, 20 violations/day):**
- Records per day: ~2,000
- Records per month: ~60,000
- Records per year: ~730,000

**Impact:** Moderate - Will benefit from partitioning after 2-3 years.

---

### 2. MODERATE-VOLUME TABLES (Optional Partitioning)

#### 2.1 **DailyReports**
**Current Configuration:** `D:\01. Projects\2025\Core product management system (CPMS)\TMS\TMS.TrackingService\src\TMS.TrackingService.Infra\Data\Configurations\DailyReportConfiguration.cs:11`

**Characteristics:**
- **Data Volume:** MODERATE
- **Insertion Rate:** Once per vehicle per day
- **Growth Rate:** Predictable and limited
- **Data Pattern:** One record per vehicle per day
- **Unique Constraint:** `(VehicleId, Date)` is unique

**Estimated Annual Growth (100 vehicles):**
- Records per day: ~100
- Records per month: ~3,000
- Records per year: ~36,500

**Impact:** LOW - Partitioning optional, only needed after 5+ years.

---

#### 2.2 **TollgateReports**
**Characteristics:**
- **Data Volume:** LOW-MODERATE
- **Insertion Rate:** Variable (depends on route)
- **Growth Rate:** ~0-10 per vehicle per day

**Impact:** LOW - Partitioning not required unless very high tollgate usage.

---

### 3. LOW-VOLUME TABLES (No Partitioning Needed)

#### 3.1 Reference/Master Data Tables
- **Vehicles** - Near-static (fleet size)
- **Drivers** - Near-static (employee count)
- **Tollgates** - Static reference data
- **MaintenanceRecords** - Low volume

#### 3.2 Summary/Aggregation Tables
- **VehiclePerformanceSummary** - Low volume (daily/weekly/monthly aggregates)
- **DriverPerformanceSummary** - Low volume (daily/weekly/monthly aggregates)

**Impact:** None - These tables remain small even with years of data.

---

## Partitioning Strategy Recommendations

### PRIORITY 1: CRITICAL (Implement Immediately)

#### **1. VehicleStatuses Table**

**Partitioning Type:** RANGE partitioning by `GpsTime` (monthly partitions)

**Strategy:**
```sql
-- Parent table (partitioned)
CREATE TABLE VehicleStatuses (
    Id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    VehicleId UUID NOT NULL,
    GpsTime TIMESTAMP WITH TIME ZONE NOT NULL,
    SysTime TIMESTAMP WITH TIME ZONE NOT NULL,
    X DOUBLE PRECISION NOT NULL,
    Y DOUBLE PRECISION NOT NULL,
    Status INTEGER NOT NULL,
    Speed INTEGER NOT NULL,
    Heading INTEGER NOT NULL,
    EventId INTEGER,
    Distance BIGINT NOT NULL,
    DriverName VARCHAR(200),
    LicenseNo VARCHAR(50),
    Address VARCHAR(500),
    SensorsJson TEXT,
    ContainerId VARCHAR(50),
    ContainerName VARCHAR(200),
    ContainerSerial VARCHAR(100),
    CreatedAt TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP WITH TIME ZONE,
    CreatedBy UUID,
    UpdatedBy UUID
) PARTITION BY RANGE (GpsTime);

-- Create monthly partitions (rolling window)
CREATE TABLE VehicleStatuses_2025_01 PARTITION OF VehicleStatuses
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE VehicleStatuses_2025_02 PARTITION OF VehicleStatuses
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- ... create partitions for next 12-24 months

-- Indexes on each partition (PostgreSQL automatically creates these)
CREATE INDEX idx_vehiclestatuses_2025_01_vehicle_gpstime
    ON VehicleStatuses_2025_01 (VehicleId, GpsTime DESC);

CREATE INDEX idx_vehiclestatuses_2025_01_gpstime
    ON VehicleStatuses_2025_01 (GpsTime DESC);
```

**Partition Retention:**
- Keep last 12 months online (hot data)
- Archive 13-24 months to separate tablespace (warm data)
- Move 24+ months to cold storage or delete

**Benefits:**
- Query performance: 10-50x faster for time-range queries
- Maintenance: Easy to drop old partitions
- Index size: Smaller indexes per partition
- Backup/restore: Partition-level operations

---

#### **2. GpsLocations Table**

**Partitioning Type:** RANGE partitioning by `Timestamp` (daily or weekly partitions)

**Strategy:**
```sql
-- Parent table (partitioned)
CREATE TABLE gps_locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id VARCHAR(100) NOT NULL,
    vehicle_plate VARCHAR(50),
    latitude DOUBLE PRECISION NOT NULL,
    longitude DOUBLE PRECISION NOT NULL,
    altitude DOUBLE PRECISION,
    speed DOUBLE PRECISION,
    heading DOUBLE PRECISION,
    accuracy DOUBLE PRECISION,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    battery_level INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    created_by UUID,
    updated_by UUID
) PARTITION BY RANGE (timestamp);

-- Create daily partitions (due to very high volume)
CREATE TABLE gps_locations_2025_01_01 PARTITION OF gps_locations
    FOR VALUES FROM ('2025-01-01') TO ('2025-01-02');

CREATE TABLE gps_locations_2025_01_02 PARTITION OF gps_locations
    FOR VALUES FROM ('2025-01-02') TO ('2025-01-03');

-- ... create partitions as needed (can be automated)

-- Indexes
CREATE INDEX idx_gps_2025_01_01_device_ts
    ON gps_locations_2025_01_01 (device_id, timestamp DESC);

CREATE INDEX idx_gps_2025_01_01_vehicle_ts
    ON gps_locations_2025_01_01 (vehicle_plate, timestamp DESC);
```

**Partition Retention:**
- Keep last 7-30 days online (hot data)
- Archive older data immediately (rarely accessed)
- Consider automatic partition cleanup job

**Benefits:**
- Prevents table bloat
- Extremely fast recent data queries
- Easy data archival/deletion
- Reduced index overhead

**⚠️ RECOMMENDATION:** Use daily partitions due to extreme data volume.

---

### PRIORITY 2: HIGH (Implement within 6-12 months)

#### **3. VehicleTrips Table**

**Partitioning Type:** RANGE partitioning by `FromTime` (monthly partitions)

```sql
CREATE TABLE VehicleTrips (
    -- columns...
) PARTITION BY RANGE (FromTime);

-- Monthly partitions
CREATE TABLE VehicleTrips_2025_01 PARTITION OF VehicleTrips
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

**Retention:** 24-36 months online, archive older

---

#### **4. DriverShifts Table**

**Partitioning Type:** RANGE partitioning by `FromTime` (monthly partitions)

**Retention:** 36 months online (compliance requirement)

---

#### **5. TelematicsViolations Table**

**Partitioning Type:** RANGE partitioning by `ViolationTime` (monthly partitions)

**Retention:** 60 months (5 years for compliance)

---

## Implementation Guidelines

### 1. Migration Approach

**For Existing Tables:**
```sql
-- Example: Migrate VehicleStatuses to partitioned table

-- Step 1: Rename existing table
ALTER TABLE VehicleStatuses RENAME TO VehicleStatuses_old;

-- Step 2: Create partitioned table
CREATE TABLE VehicleStatuses (...) PARTITION BY RANGE (GpsTime);

-- Step 3: Create partitions
-- (Create partitions for historical and future data)

-- Step 4: Migrate data
INSERT INTO VehicleStatuses SELECT * FROM VehicleStatuses_old;

-- Step 5: Verify and drop old table
DROP TABLE VehicleStatuses_old;
```

### 2. Partition Maintenance Automation

**Create automated partition management:**

```sql
-- PostgreSQL function to automatically create future partitions
CREATE OR REPLACE FUNCTION create_partition_if_not_exists(
    parent_table TEXT,
    partition_date DATE
) RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    partition_name := parent_table || '_' || TO_CHAR(partition_date, 'YYYY_MM');
    start_date := DATE_TRUNC('month', partition_date);
    end_date := start_date + INTERVAL '1 month';

    IF NOT EXISTS (
        SELECT 1 FROM pg_class WHERE relname = partition_name
    ) THEN
        EXECUTE format(
            'CREATE TABLE %I PARTITION OF %I FOR VALUES FROM (%L) TO (%L)',
            partition_name, parent_table, start_date, end_date
        );

        -- Create indexes
        EXECUTE format(
            'CREATE INDEX idx_%s_vehicle_time ON %I (VehicleId, GpsTime DESC)',
            partition_name, partition_name
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Schedule via pg_cron or application job
SELECT create_partition_if_not_exists('VehicleStatuses', CURRENT_DATE + INTERVAL '2 months');
```

### 3. Partition Cleanup/Archival

**Automated partition archival:**

```sql
-- Function to archive old partitions
CREATE OR REPLACE FUNCTION archive_old_partition(
    parent_table TEXT,
    retention_months INTEGER
) RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    cutoff_date DATE;
BEGIN
    cutoff_date := DATE_TRUNC('month', CURRENT_DATE - (retention_months || ' months')::INTERVAL);

    -- Find partitions older than cutoff
    FOR partition_name IN
        SELECT tablename FROM pg_tables
        WHERE tablename LIKE parent_table || '%'
        AND tablename < parent_table || '_' || TO_CHAR(cutoff_date, 'YYYY_MM')
    LOOP
        -- Option 1: Detach and move to archive tablespace
        EXECUTE format('ALTER TABLE %I DETACH PARTITION %I', parent_table, partition_name);

        -- Option 2: Export and drop
        -- EXECUTE format('COPY %I TO ''/archive/%s.csv'' CSV HEADER', partition_name, partition_name);
        -- EXECUTE format('DROP TABLE %I', partition_name);
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

---

## Performance Optimization Strategies

### 1. Partition Pruning
- Ensure queries include partition key in WHERE clause
- Use `GpsTime`/`Timestamp` in all time-range queries
- Avoid full table scans

### 2. Partition-wise Joins
- When joining partitioned tables, align on partition keys
- PostgreSQL can perform partition-wise joins for better performance

### 3. Parallel Query Execution
- PostgreSQL can query multiple partitions in parallel
- Configure `max_parallel_workers_per_gather` appropriately

### 4. Index Strategy per Partition
- Each partition should have indexes for common queries
- Smaller partition indexes = faster index scans
- Consider partial indexes for specific conditions

---

## Monitoring & Maintenance

### 1. Partition Size Monitoring
```sql
-- Monitor partition sizes
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
FROM pg_tables
WHERE tablename LIKE 'VehicleStatuses_%'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 2. Query Performance Monitoring
```sql
-- Check if partition pruning is working
EXPLAIN ANALYZE
SELECT * FROM VehicleStatuses
WHERE GpsTime BETWEEN '2025-01-01' AND '2025-01-31';

-- Should show: "Partitions pruned: X"
```

### 3. Automated Maintenance Schedule
- **Daily:** Create partitions for next 60 days
- **Weekly:** Verify partition sizes and query performance
- **Monthly:** Archive partitions older than retention period
- **Quarterly:** Review partition strategy and adjust

---

## Summary & Recommendations

### Immediate Actions (Month 1)
1. ✅ Implement partitioning for `VehicleStatuses` (monthly partitions)
2. ✅ Implement partitioning for `GpsLocations` (daily partitions)
3. ✅ Set up automated partition creation job
4. ✅ Set up partition monitoring

### Short-term Actions (Months 2-6)
1. ✅ Implement partitioning for `VehicleTrips` (monthly partitions)
2. ✅ Implement partitioning for `DriverShifts` (monthly partitions)
3. ✅ Implement partitioning for `TelematicsViolations` (monthly partitions)
4. ✅ Set up automated partition archival job

### Long-term Strategy
1. ✅ Monitor partition performance and adjust strategies
2. ✅ Consider sub-partitioning by VehicleId for very high volumes
3. ✅ Implement partition-aware application queries
4. ✅ Regular review of retention policies

---

## Expected Performance Improvements

| Table | Before Partitioning | After Partitioning | Improvement |
|-------|-------------------|-------------------|-------------|
| VehicleStatuses (1 year data) | 5-15 seconds | 0.1-0.5 seconds | **30-50x faster** |
| GpsLocations (30 days data) | 10-30 seconds | 0.05-0.2 seconds | **50-200x faster** |
| VehicleTrips (1 year data) | 2-5 seconds | 0.1-0.3 seconds | **20-50x faster** |

---

## Conclusion

Implementing table partitioning is **CRITICAL** for the long-term scalability and performance of the TMS Tracking Service. Without partitioning:
- **VehicleStatuses** will degrade within 6-12 months
- **GpsLocations** will become unmanageable within 3-6 months
- Query performance will suffer exponentially
- Backup/restore operations will take hours instead of minutes

With proper partitioning:
- ✅ Predictable query performance as data grows
- ✅ Efficient data archival and retention management
- ✅ Reduced index overhead
- ✅ Easier maintenance and troubleshooting
- ✅ Better resource utilization

**Priority Implementation Order:**
1. **GpsLocations** (CRITICAL - highest volume)
2. **VehicleStatuses** (CRITICAL - high volume)
3. **VehicleTrips** (HIGH)
4. **DriverShifts** (MEDIUM)
5. **TelematicsViolations** (MEDIUM)

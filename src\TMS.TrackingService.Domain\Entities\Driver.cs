using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

/// <summary>
/// Driver entity
/// </summary>
public class Driver : AuditableEntity<Guid>
{
    public int VietmapId { get; set; }
    public string Code { get; set; } = string.Empty;
    public string? Rfid { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string? Address { get; set; }
    public string? LicenseNo { get; set; }
    public DateTime? LicenseExpireDate { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? Remark { get; set; }

    // Navigation properties
    public ICollection<DriverShift> DriverShifts { get; set; } = new List<DriverShift>();
    public ICollection<TelematicsViolation> TelematicsViolations { get; set; } = new List<TelematicsViolation>();
}

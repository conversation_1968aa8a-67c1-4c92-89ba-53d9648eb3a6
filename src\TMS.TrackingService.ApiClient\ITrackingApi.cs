﻿using TMS.TrackingService.Contracts.Tracking;
using TMS.TrackingService.Contracts.Vietmap;
using Refit;

namespace TMS.TrackingService.ApiClient;

/// <summary>
/// Refit interface for Vietmap Tracking API
/// Base URL: https://client-api.quanlyxe.vn
/// </summary>
public interface IVietmapTrackingApi
{
    /// <summary>
    /// API 1: Get list of vehicles for user
    /// </summary>
    [Get("/v3/tracking/getvehicles")]
    Task<VietmapDataResponse<List<VehicleDto>>> GetVehiclesAsync();

    /// <summary>
    /// API 2: Get vehicle GPS status (real-time)
    /// </summary>
    [Get("/v3/tracking/GetVehicleStatus")]
    Task<VietmapDataResponse<List<VehicleStatusDto>>> GetVehicleStatusAsync([Query] string plate);

    /// <summary>
    /// API 3: Get vehicle GPS history for a time period
    /// </summary>
    [Get("/v3/tracking/getvehiclehistory")]
    Task<VietmapDataResponse<List<VehicleHistoryDto>>> GetVehicleHistoryAsync(
        [Query] int id,
        [Query] string from,
        [Query] string to);

    /// <summary>
    /// API 4: Get vehicle images for a time period
    /// </summary>
    [Get("/v3/tracking/getvehicleimages")]
    Task<VietmapDataResponse<List<VehicleImageDto>>> GetVehicleImagesAsync(
        [Query] int id,
        [Query] string from,
        [Query] string to);

    /// <summary>
    /// API 5: Get daily distance report
    /// </summary>
    [Get("/v3/tracking/GetDailyReports")]
    Task<VietmapDataResponse<List<DailyReportDto>>> GetDailyReportsAsync(
        [Query] int id,
        [Query] string from,
        [Query] string to);

    /// <summary>
    /// API 6: Get point in/out report
    /// </summary>
    [Get("/v3/tracking/GetVehiclePointReports")]
    Task<VietmapDataResponse<List<VehiclePointReportDto>>> GetVehiclePointReportsAsync(
        [Query] int id,
        [Query] string from,
        [Query] string to);

    /// <summary>
    /// API 7: Get tollgate in/out report
    /// </summary>
    [Get("/v3/tracking/GetVehicleTollgateReports")]
    Task<VietmapDataResponse<List<VehicleTollgateReportDto>>> GetVehicleTollgateReportsAsync(
        [Query] int id,
        [Query] string from,
        [Query] string to);

    /// <summary>
    /// API 8: Get driver shift summary report
    /// </summary>
    [Get("/v3/tracking/GetVehicleShiftReports")]
    Task<VietmapDataResponse<List<VehicleShiftReportDto>>> GetVehicleShiftReportsAsync(
        [Query] int id,
        [Query] string from,
        [Query] string to);

    /// <summary>
    /// API 9: Get vehicle stop report
    /// </summary>
    [Get("/v3/tracking/GetVehicleStopReports")]
    Task<VietmapDataResponse<List<VehicleStopReportDto>>> GetVehicleStopReportsAsync(
        [Query] int id,
        [Query] string from,
        [Query] string to,
        [Query] int duration);

    /// <summary>
    /// API 10: Get vehicle maintenance report
    /// </summary>
    [Get("/v3/tracking/GetVehicleMaintenanceReport")]
    Task<VietmapDataResponse<List<VehicleMaintenanceReportDto>>> GetVehicleMaintenanceReportAsync(
        [Query] int? mtype = null,
        [Query] string? vehicles = null,
        [Query] string? from = null,
        [Query] string? to = null);

    /// <summary>
    /// API 11: Get temperature sensor report
    /// </summary>
    [Get("/v3/tracking/GetVehicleTemperatureReport")]
    Task<VietmapDataResponse<List<VehicleTemperatureReportDto>>> GetVehicleTemperatureReportAsync(
        [Query] string vehicle,
        [Query] string from,
        [Query] string to,
        [Query] double? filter = null);

    /// <summary>
    /// API 12: Get maintenance management
    /// </summary>
    [Get("/v3/tracking/GetMaintenanceManagement")]
    Task<MaintenanceManagementResponse> GetMaintenanceManagementAsync(
        [Query] string? plate = null,
        [Query] int? vehicleGroupId = null,
        [Query] int? type = null,
        [Query] int? page = null,
        [Query] int? pageSize = null);

    /// <summary>
    /// API 13: Get list of drivers
    /// </summary>
    [Get("/v3/tracking/GetDrivers")]
    Task<VietmapDataResponse<List<DriverDto>>> GetDriversAsync();

    /// <summary>
    /// API 14: Get fuel and distance report by sensor
    /// </summary>
    [Get("/v3/tracking/GetVehicleFuelReport")]
    Task<VietmapDataResponse<List<VehicleFuelReportDto>>> GetVehicleFuelReportAsync(
        [Query] string vehicles,
        [Query] string from,
        [Query] string to);

    /// <summary>
    /// API 15: Get trip report
    /// </summary>
    [Get("/v3/tracking/GetVehicleTripReport")]
    Task<VietmapDataResponse<List<VehicleTripReportDto>>> GetVehicleTripReportAsync(
        [Query] string vehicles,
        [Query] string from,
        [Query] string to);

    /// <summary>
    /// API 16: Get list of tollgates
    /// </summary>
    [Get("/v3/tracking/gettollgates")]
    Task<VietmapDataResponse<List<TollgateDto>>> GetTollgatesAsync();

    /// <summary>
    /// API 17: Get telematics violations report (overspeed, harsh braking, acceleration, etc.)
    /// </summary>
    [Get("/v3/tracking/gettelematicsviolation")]
    Task<VietmapDataResponse<List<TelematicsViolationDto>>> GetTelematicsViolationAsync(
        [Query] string? driverIds = null,
        [Query] string? from = null,
        [Query] string? to = null);
}


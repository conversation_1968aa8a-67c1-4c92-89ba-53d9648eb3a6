using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.TrackingService.Contracts.Vietmap;
using TMS.TrackingService.Infra.Data;

namespace TMS.TrackingService.Application.Features.Tracking.Queries.GetVehicles;

public class GetVehiclesQueryHandler : IRequestHandler<GetVehiclesQuery, List<VehicleDto>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<GetVehiclesQueryHandler> _logger;

    public GetVehiclesQueryHandler(ApplicationDbContext dbContext, IMapper mapper, ILogger<GetVehiclesQueryHandler> logger)
    {
        _dbContext = dbContext;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<List<VehicleDto>> Handle(GetVehiclesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var vehicles = await _dbContext.Vehicles
                .AsNoTracking()
                .OrderBy(v => v.Plate)
                .ToListAsync(cancellationToken);

            return _mapper.Map<List<VehicleDto>>(vehicles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching vehicles from database");
            return new List<VehicleDto>();
        }
    }
}

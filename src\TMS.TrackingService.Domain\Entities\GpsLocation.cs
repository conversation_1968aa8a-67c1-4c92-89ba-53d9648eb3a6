using TMS.SharedKernel.Domain.Entities;

namespace TMS.TrackingService.Domain.Entities;

public class GpsLocation : AuditableEntity<Guid>
{
    public Guid Id { get; set; }

    /// <summary>
    /// Device ID or Mobile ID sending the GPS data
    /// </summary>
    public string DeviceId { get; set; } = string.Empty;

    /// <summary>
    /// Vehicle plate number (optional)
    /// </summary>
    public string? VehiclePlate { get; set; }

    /// <summary>
    /// Latitude coordinate
    /// </summary>
    public double Latitude { get; set; }

    /// <summary>
    /// Longitude coordinate
    /// </summary>
    public double Longitude { get; set; }

    /// <summary>
    /// Altitude in meters (optional)
    /// </summary>
    public double? Altitude { get; set; }

    /// <summary>
    /// Speed in km/h (optional)
    /// </summary>
    public double? Speed { get; set; }

    /// <summary>
    /// Heading/Direction in degrees (0-360) (optional)
    /// </summary>
    public double? Heading { get; set; }

    /// <summary>
    /// Accuracy in meters (optional)
    /// </summary>
    public double? Accuracy { get; set; }

    /// <summary>
    /// Timestamp when GPS data was captured on the device
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Battery level percentage (optional)
    /// </summary>
    public int? BatteryLevel { get; set; }
}
